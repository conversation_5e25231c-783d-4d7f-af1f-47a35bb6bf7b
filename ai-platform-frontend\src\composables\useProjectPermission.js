import { computed } from 'vue'
import useUserStore from '@/store/modules/user'
import { 
  isAdmin, 
  isProjectManager, 
  isProjectMember, 
  isCommonUser,
  hasProjectManagePermission,
  hasTaskManagePermission,
  hasDatabaseManagePermission,
  hasToolUsePermission,
  checkTaskPermission,
  checkProjectPermission,
  shouldShowMenu,
  shouldShowButton
} from '@/utils/projectPermission'

/**
 * 项目权限相关的组合式API
 */
export function useProjectPermission() {
  const userStore = useUserStore()

  // 用户角色检查
  const roleChecks = {
    isAdmin: computed(() => isAdmin()),
    isProjectManager: computed(() => isProjectManager()),
    isProjectMember: computed(() => isProjectMember()),
    isCommonUser: computed(() => isCommonUser())
  }

  // 权限检查
  const permissionChecks = {
    hasProjectManagePermission: computed(() => hasProjectManagePermission()),
    hasTaskManagePermission: computed(() => hasTaskManagePermission()),
    hasDatabaseManagePermission: computed(() => hasDatabaseManagePermission()),
    hasToolUsePermission: computed(() => hasToolUsePermission())
  }

  // 菜单显示检查（适配现有菜单结构）
  const menuChecks = {
    shouldShowHomeMenu: computed(() => shouldShowMenu('index')),
    shouldShowToolsMenu: computed(() => shouldShowMenu('tools')),
    shouldShowProjectsMenu: computed(() => shouldShowMenu('projects')),
    shouldShowKnowledgeBasesMenu: computed(() => shouldShowMenu('knowledge-bases')),
    shouldShowSystemMenu: computed(() => shouldShowMenu('system'))
  }

  // 任务权限检查函数
  const checkTaskPermissionFunc = (task, action = 'view') => {
    return checkTaskPermission(task, action)
  }

  // 项目权限检查函数
  const checkProjectPermissionFunc = (project, action = 'view') => {
    return checkProjectPermission(project, action)
  }

  // 按钮显示检查函数
  const shouldShowButtonFunc = (permissions) => {
    return shouldShowButton(permissions)
  }

  // 获取用户信息
  const userInfo = computed(() => ({
    id: userStore.id,
    name: userStore.name,
    roles: userStore.roles,
    permissions: userStore.permissions
  }))

  // 检查用户是否是任务创建者
  const isTaskCreator = (task) => {
    return task.assignedTo === userStore.id
  }

  // 检查用户是否是项目所有者
  const isProjectOwner = (project) => {
    return project.ownerId === userStore.id
  }

  // 获取用户可访问的菜单配置（适配现有菜单结构）
  const getAccessibleMenus = () => {
    const menus = []

    // 首页 - 所有用户都可以访问
    menus.push({
      name: 'index',
      path: '/index',
      title: '首页',
      icon: 'dashboard',
      component: 'home/index'
    })

    // 工具广场
    if (permissionChecks.hasToolUsePermission.value) {
      menus.push({
        name: 'tools',
        path: '/tools',
        title: '工具广场',
        icon: 'tools',
        component: 'tools/index'
      })
    }

    // 项目看板
    if (permissionChecks.hasProjectManagePermission.value) {
      menus.push({
        name: 'projects',
        path: '/projects',
        title: '项目看板',
        icon: 'kanban',
        component: 'projects/board'
      })
    }

    // 数据库管理
    if (permissionChecks.hasDatabaseManagePermission.value) {
      menus.push({
        name: 'knowledge-bases',
        path: '/knowledge-bases',
        title: '数据库管理',
        icon: 'database',
        component: 'knowledge-bases/index'
      })
    }

    return menus
  }

  // 检查路由权限
  const checkRoutePermission = (route) => {
    // 如果路由没有设置权限要求，则默认允许访问
    if (!route.meta || (!route.meta.roles && !route.meta.permissions)) {
      return true
    }
    
    // 超级管理员可以访问所有路由
    if (roleChecks.isAdmin.value) {
      return true
    }
    
    // 检查角色权限
    if (route.meta.roles) {
      const requiredRoles = route.meta.roles
      
      if (requiredRoles.includes('project_manager') && roleChecks.isProjectManager.value) {
        return true
      }
      
      if (requiredRoles.includes('project_member') && roleChecks.isProjectMember.value) {
        return true
      }
      
      if (requiredRoles.includes('common') && roleChecks.isCommonUser.value) {
        return true
      }
    }
    
    // 检查权限标识
    if (route.meta.permissions) {
      return shouldShowButtonFunc(route.meta.permissions)
    }
    
    return false
  }

  return {
    // 角色检查
    ...roleChecks,
    
    // 权限检查
    ...permissionChecks,
    
    // 菜单检查
    ...menuChecks,
    
    // 用户信息
    userInfo,
    
    // 功能函数
    checkTaskPermission: checkTaskPermissionFunc,
    checkProjectPermission: checkProjectPermissionFunc,
    shouldShowButton: shouldShowButtonFunc,
    isTaskCreator,
    isProjectOwner,
    getAccessibleMenus,
    checkRoutePermission
  }
}
