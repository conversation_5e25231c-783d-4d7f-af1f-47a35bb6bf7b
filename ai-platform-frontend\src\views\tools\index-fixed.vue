<template>
  <div class="tools-container">
    <div class="tools-header">
      <h1>工具广场</h1>
      <p>共 {{ tools.length }} 个工具，显示 {{ filteredTools.length }} 个</p>
    </div>
    
    <!-- 搜索和筛选 -->
    <div class="tools-filter">
      <el-input
        v-model="searchQuery"
        placeholder="搜索工具..."
        style="width: 300px; margin-right: 20px;"
        @input="handleSearch"
      >
        <template #prefix>
          <el-icon><Search /></el-icon>
        </template>
      </el-input>
      <el-select
        v-model="selectedType"
        placeholder="选择类型"
        style="width: 200px;"
        @change="handleTypeChange"
      >
        <el-option label="全部类型" value="" />
        <el-option
          v-for="type in types"
          :key="type.typeId"
          :label="type.displayName"
          :value="type.typeId"
        />
      </el-select>
    </div>
    
    <!-- 调试信息 -->
    <div v-if="tools.length === 0 && !loading" class="debug-info">
      <el-alert
        title="调试信息"
        type="info"
        :closable="false"
        show-icon
      >
        <p>工具数据: {{ tools.length }} 个</p>
        <p>过滤后数据: {{ filteredTools.length }} 个</p>
        <p>搜索关键词: "{{ searchQuery }}"</p>
        <p>选择类型: {{ selectedType }}</p>
        <el-button size="small" @click="debugLog">查看控制台日志</el-button>
      </el-alert>
    </div>
    
    <!-- 工具列表 -->
    <div class="tools-grid">
      <div v-if="loading" class="loading">
        <el-icon class="is-loading"><Loading /></el-icon>
        <span>加载中...</span>
      </div>
      
      <div v-else-if="filteredTools.length === 0" class="empty">
        <el-empty description="暂无工具数据">
          <el-button type="primary" @click="fetchTools">重新加载</el-button>
        </el-empty>
      </div>
      
      <div v-else class="tools-list">
        <div
          v-for="tool in filteredTools"
          :key="tool.toolId"
          class="tool-item"
        >
          <div class="tool-header">
            <h3>{{ tool.toolName }}</h3>
            <el-tag :type="getHealthTagType(tool.isHealth)">
              {{ tool.isHealth ? '健康' : '异常' }}
            </el-tag>
          </div>
          
          <div class="tool-content">
            <p class="tool-description">{{ tool.description }}</p>
            
            <div class="tool-meta">
              <div class="meta-item">
                <span class="label">供应商:</span>
                <span class="value">{{ tool.vendor }}</span>
              </div>
              <div class="meta-item">
                <span class="label">版本:</span>
                <span class="value">{{ tool.version }}</span>
              </div>
              <div class="meta-item">
                <span class="label">类型:</span>
                <span class="value">{{ getTypeName(tool.typeId) }}</span>
              </div>
            </div>
            
            <div class="tool-actions">
              <el-button type="primary" size="small" @click="useTool(tool)">
                使用工具
              </el-button>
              <el-button size="small" @click="viewDetails(tool)">
                查看详情
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { Search, Loading } from '@element-plus/icons-vue'
import { getToolsList } from '@/api/tools'
import { getTypesList } from '@/api/types'
import { ElMessage } from 'element-plus'

// 响应式数据
const tools = ref([])
const types = ref([])
const loading = ref(false)
const searchQuery = ref('')
const selectedType = ref('')

// 计算属性 - 过滤后的工具
const filteredTools = computed(() => {
  let filtered = tools.value
  
  // 按类型过滤
  if (selectedType.value) {
    filtered = filtered.filter(tool => tool.typeId === selectedType.value)
  }
  
  // 按搜索关键词过滤
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(tool => 
      tool.toolName.toLowerCase().includes(query) ||
      tool.description.toLowerCase().includes(query) ||
      tool.vendor.toLowerCase().includes(query)
    )
  }
  
  return filtered
})

// 获取工具列表
const fetchTools = async () => {
  loading.value = true
  try {
    console.log('开始获取工具列表...')
    
    const response = await getToolsList({
      pageNum: 1,
      pageSize: 100
    })
    
    console.log('工具列表API响应:', response)
    
    if (response.code === 200) {
      tools.value = response.rows || []
      console.log('成功获取工具列表:', tools.value.length, '个工具')
      
      if (tools.value.length === 0) {
        ElMessage.warning('暂无工具数据')
      } else {
        ElMessage.success(`成功加载 ${tools.value.length} 个工具`)
      }
    } else {
      console.error('获取工具列表失败:', response.msg)
      ElMessage.error(response.msg || '获取工具列表失败')
    }
  } catch (error) {
    console.error('获取工具列表异常:', error)
    ElMessage.error('获取工具列表失败: ' + error.message)
  } finally {
    loading.value = false
  }
}

// 获取类型列表
const fetchTypes = async () => {
  try {
    console.log('开始获取类型列表...')
    
    const response = await getTypesList({
      pageNum: 1,
      pageSize: 100,
      isActive: 1
    })
    
    console.log('类型列表API响应:', response)
    
    if (response.code === 200) {
      types.value = response.rows || []
      console.log('成功获取类型列表:', types.value.length, '个类型')
    } else {
      console.error('获取类型列表失败:', response.msg)
    }
  } catch (error) {
    console.error('获取类型列表异常:', error)
  }
}

// 根据类型ID获取类型名称
const getTypeName = (typeId) => {
  const type = types.value.find(t => t.typeId === typeId)
  return type ? type.displayName : '未知类型'
}

// 获取健康状态标签类型
const getHealthTagType = (isHealth) => {
  return isHealth ? 'success' : 'danger'
}

// 搜索处理
const handleSearch = () => {
  console.log('搜索关键词:', searchQuery.value)
}

// 类型变更处理
const handleTypeChange = () => {
  console.log('选择类型:', selectedType.value)
}

// 使用工具
const useTool = (tool) => {
  console.log('使用工具:', tool.toolName)
  ElMessage.info(`准备使用工具: ${tool.toolName}`)
}

// 查看详情
const viewDetails = (tool) => {
  console.log('查看工具详情:', tool.toolName)
  ElMessage.info(`查看工具详情: ${tool.toolName}`)
}

// 调试日志方法
const debugLog = () => {
  console.log('=== 工具广场调试信息 ===')
  console.log('工具数据:', tools.value)
  console.log('过滤后数据:', filteredTools.value)
  console.log('类型数据:', types.value)
  console.log('搜索关键词:', searchQuery.value)
  console.log('选择类型:', selectedType.value)
  console.log('加载状态:', loading.value)
  console.log('========================')
}

// 页面挂载时获取数据
onMounted(() => {
  console.log('工具广场页面挂载')
  fetchTypes()
  fetchTools()
})
</script>

<style scoped>
.tools-container {
  padding: 20px;
  background: #f5f7fa;
  min-height: 100vh;
}

.tools-header {
  margin-bottom: 20px;
}

.tools-header h1 {
  margin: 0 0 10px 0;
  color: #303133;
}

.tools-header p {
  margin: 0;
  color: #606266;
}

.tools-filter {
  margin-bottom: 20px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.tools-grid {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 20px;
}

.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
  color: #606266;
}

.loading .el-icon {
  margin-right: 10px;
  font-size: 20px;
}

.empty {
  padding: 40px;
}

.debug-info {
  margin-bottom: 20px;
}

.tools-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
}

.tool-item {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 20px;
  background: #fafafa;
  transition: all 0.3s;
}

.tool-item:hover {
  border-color: #409eff;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.tool-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.tool-header h3 {
  margin: 0;
  color: #303133;
  font-size: 16px;
}

.tool-content {
  color: #606266;
}

.tool-description {
  margin: 0 0 15px 0;
  line-height: 1.5;
}

.tool-meta {
  margin-bottom: 15px;
}

.meta-item {
  display: flex;
  margin-bottom: 5px;
}

.meta-item .label {
  width: 60px;
  color: #909399;
  font-size: 12px;
}

.meta-item .value {
  color: #606266;
  font-size: 12px;
}

.tool-actions {
  display: flex;
  gap: 10px;
}
</style>
