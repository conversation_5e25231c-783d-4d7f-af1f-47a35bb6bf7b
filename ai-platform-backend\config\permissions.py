"""
权限常量定义
用于统一管理系统中的权限标识
"""


class RoleKeys:
    """角色权限字符串常量"""
    
    # 超级管理员
    ADMIN = 'admin'
    
    # 项目管理员
    PROJECT_MANAGER = 'project_manager'
    
    # 项目成员
    PROJECT_MEMBER = 'project_member'
    
    # 普通用户
    COMMON = 'common'


class Permissions:
    """权限标识常量"""
    
    # 超级权限
    ALL = '*:*:*'
    
    # 系统管理权限
    class System:
        # 用户管理
        USER_LIST = 'system:user:list'
        USER_QUERY = 'system:user:query'
        USER_ADD = 'system:user:add'
        USER_EDIT = 'system:user:edit'
        USER_REMOVE = 'system:user:remove'
        USER_EXPORT = 'system:user:export'
        USER_IMPORT = 'system:user:import'
        USER_RESET_PWD = 'system:user:resetPwd'
        
        # 角色管理
        ROLE_LIST = 'system:role:list'
        ROLE_QUERY = 'system:role:query'
        ROLE_ADD = 'system:role:add'
        ROLE_EDIT = 'system:role:edit'
        ROLE_REMOVE = 'system:role:remove'
        ROLE_EXPORT = 'system:role:export'
        
        # 菜单管理
        MENU_LIST = 'system:menu:list'
        MENU_QUERY = 'system:menu:query'
        MENU_ADD = 'system:menu:add'
        MENU_EDIT = 'system:menu:edit'
        MENU_REMOVE = 'system:menu:remove'
        
        # 部门管理
        DEPT_LIST = 'system:dept:list'
        DEPT_QUERY = 'system:dept:query'
        DEPT_ADD = 'system:dept:add'
        DEPT_EDIT = 'system:dept:edit'
        DEPT_REMOVE = 'system:dept:remove'
    
    # 业务权限
    class Business:
        # 项目管理
        PROJECT_LIST = 'business:projects:list'
        PROJECT_QUERY = 'business:projects:query'
        PROJECT_ADD = 'business:projects:add'
        PROJECT_EDIT = 'business:projects:edit'
        PROJECT_REMOVE = 'business:projects:remove'
        PROJECT_EXPORT = 'business:projects:export'
        
        # 项目成员管理
        PROJECT_MEMBER_LIST = 'business:project-members:list'
        PROJECT_MEMBER_QUERY = 'business:project-members:query'
        PROJECT_MEMBER_ADD = 'business:project-members:add'
        PROJECT_MEMBER_EDIT = 'business:project-members:edit'
        PROJECT_MEMBER_REMOVE = 'business:project-members:remove'
        
        # 任务管理
        TASK_LIST = 'business:tasks:list'
        TASK_QUERY = 'business:tasks:query'
        TASK_ADD = 'business:tasks:add'
        TASK_EDIT = 'business:tasks:edit'
        TASK_REMOVE = 'business:tasks:remove'
        TASK_EXPORT = 'business:tasks:export'
        
        # 知识库管理
        KB_LIST = 'business:knowledge-bases:list'
        KB_QUERY = 'business:knowledge-bases:query'
        KB_ADD = 'business:knowledge-bases:add'
        KB_EDIT = 'business:knowledge-bases:edit'
        KB_REMOVE = 'business:knowledge-bases:remove'
        
        # 工具管理
        TOOL_LIST = 'business:tools:list'
        TOOL_QUERY = 'business:tools:query'
        TOOL_USE = 'business:tools:use'


class ProjectRoles:
    """项目内角色常量"""
    
    # 项目管理员
    MANAGER = 'manager'
    
    # 项目成员
    MEMBER = 'member'


def get_role_permissions(role_key: str) -> list:
    """
    根据角色获取对应的权限列表
    
    :param role_key: 角色权限字符串
    :return: 权限列表
    """
    role_permissions = {
        RoleKeys.ADMIN: [Permissions.ALL],
        
        RoleKeys.PROJECT_MANAGER: [
            # 项目管理权限
            Permissions.Business.PROJECT_LIST,
            Permissions.Business.PROJECT_QUERY,
            Permissions.Business.PROJECT_ADD,
            Permissions.Business.PROJECT_EDIT,
            Permissions.Business.PROJECT_REMOVE,
            Permissions.Business.PROJECT_EXPORT,
            
            # 项目成员管理权限
            Permissions.Business.PROJECT_MEMBER_LIST,
            Permissions.Business.PROJECT_MEMBER_QUERY,
            Permissions.Business.PROJECT_MEMBER_ADD,
            Permissions.Business.PROJECT_MEMBER_EDIT,
            Permissions.Business.PROJECT_MEMBER_REMOVE,
            
            # 任务管理权限
            Permissions.Business.TASK_LIST,
            Permissions.Business.TASK_QUERY,
            Permissions.Business.TASK_ADD,
            Permissions.Business.TASK_EDIT,
            Permissions.Business.TASK_REMOVE,
            Permissions.Business.TASK_EXPORT,
            
            # 知识库管理权限
            Permissions.Business.KB_LIST,
            Permissions.Business.KB_QUERY,
            Permissions.Business.KB_ADD,
            Permissions.Business.KB_EDIT,
            Permissions.Business.KB_REMOVE,
            
            # 工具使用权限
            Permissions.Business.TOOL_LIST,
            Permissions.Business.TOOL_QUERY,
            Permissions.Business.TOOL_USE,
        ],
        
        RoleKeys.PROJECT_MEMBER: [
            # 项目查看权限
            Permissions.Business.PROJECT_LIST,
            Permissions.Business.PROJECT_QUERY,
            
            # 任务管理权限（有限）
            Permissions.Business.TASK_LIST,
            Permissions.Business.TASK_QUERY,
            Permissions.Business.TASK_ADD,
            
            # 工具使用权限
            Permissions.Business.TOOL_LIST,
            Permissions.Business.TOOL_QUERY,
            Permissions.Business.TOOL_USE,
        ],
        
        RoleKeys.COMMON: [
            # 工具使用权限
            Permissions.Business.TOOL_LIST,
            Permissions.Business.TOOL_QUERY,
            Permissions.Business.TOOL_USE,
        ]
    }
    
    return role_permissions.get(role_key, [])


def check_permission(user_permissions: list, required_permission: str) -> bool:
    """
    检查用户是否具有指定权限
    
    :param user_permissions: 用户权限列表
    :param required_permission: 需要的权限
    :return: 是否有权限
    """
    if Permissions.ALL in user_permissions:
        return True
    
    return required_permission in user_permissions


def check_role(user_roles: list, required_role: str) -> bool:
    """
    检查用户是否具有指定角色
    
    :param user_roles: 用户角色列表
    :param required_role: 需要的角色
    :return: 是否有角色
    """
    if RoleKeys.ADMIN in user_roles:
        return True
    
    return required_role in user_roles


def get_user_accessible_projects(user_id: int, user_roles: list) -> str:
    """
    根据用户角色生成项目访问权限的SQL条件
    
    :param user_id: 用户ID
    :param user_roles: 用户角色列表
    :return: SQL条件字符串
    """
    if RoleKeys.ADMIN in user_roles:
        return "1=1"  # 超级管理员可以访问所有项目
    
    if RoleKeys.PROJECT_MANAGER in user_roles or RoleKeys.PROJECT_MEMBER in user_roles:
        # 项目管理员和项目成员可以访问自己参与的项目
        return f"""(
            rd_projects.owner_id = {user_id} 
            OR rd_projects.project_id IN (
                SELECT project_id FROM rd_project_members WHERE user_id = {user_id}
            )
        )"""
    
    # 普通用户无法访问任何项目
    return "1=0"
