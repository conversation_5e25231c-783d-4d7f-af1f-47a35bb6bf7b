<template>
  <div class="debug-container">
    <h2>工具广场调试页面</h2>
    
    <!-- API响应调试 -->
    <el-card class="debug-card">
      <template #header>
        <span>API响应调试</span>
        <el-button type="primary" @click="testApi">测试API</el-button>
      </template>
      
      <div class="debug-section">
        <h4>API状态: {{ apiStatus }}</h4>
        <h4>工具数量: {{ tools.length }}</h4>
        <h4>类型数量: {{ types.length }}</h4>
        
        <el-divider />
        
        <h4>原始API响应:</h4>
        <pre class="json-display">{{ JSON.stringify(rawResponse, null, 2) }}</pre>
        
        <el-divider />
        
        <h4>处理后的工具数据:</h4>
        <pre class="json-display">{{ JSON.stringify(tools, null, 2) }}</pre>
      </div>
    </el-card>
    
    <!-- 渲染测试 -->
    <el-card class="debug-card">
      <template #header>
        <span>渲染测试</span>
      </template>
      
      <div class="debug-section">
        <h4>简单列表渲染:</h4>
        <ul>
          <li v-for="tool in tools" :key="tool.toolId">
            {{ tool.toolName }} - {{ tool.description }}
          </li>
        </ul>
        
        <el-divider />
        
        <h4>卡片渲染测试:</h4>
        <el-row :gutter="20">
          <el-col v-for="tool in tools" :key="tool.toolId" :span="8">
            <el-card class="tool-card" shadow="hover">
              <template #header>
                <span>{{ tool.toolName }}</span>
              </template>
              <p>{{ tool.description }}</p>
              <p>供应商: {{ tool.vendor }}</p>
              <p>版本: {{ tool.version }}</p>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </el-card>
    
    <!-- 控制台日志 -->
    <el-card class="debug-card">
      <template #header>
        <span>控制台日志</span>
        <el-button @click="clearLogs">清空日志</el-button>
      </template>
      
      <div class="debug-section">
        <div v-for="(log, index) in logs" :key="index" class="log-item">
          <span class="log-time">{{ log.time }}</span>
          <span :class="'log-' + log.level">{{ log.message }}</span>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { getToolsList } from '@/api/tools'
import { getTypesList } from '@/api/types'
import { ElMessage } from 'element-plus'

// 数据
const tools = ref([])
const types = ref([])
const rawResponse = ref({})
const apiStatus = ref('未测试')
const logs = ref([])

// 添加日志
const addLog = (level, message) => {
  logs.value.unshift({
    time: new Date().toLocaleTimeString(),
    level,
    message
  })
  console[level](message)
}

// 清空日志
const clearLogs = () => {
  logs.value = []
}

// 测试API
const testApi = async () => {
  addLog('info', '开始测试工具列表API...')
  
  try {
    apiStatus.value = '请求中...'
    
    const response = await getToolsList({
      pageNum: 1,
      pageSize: 10
    })
    
    addLog('info', `API响应状态: ${response.code}`)
    addLog('info', `API响应消息: ${response.msg}`)
    addLog('info', `数据行数: ${response.rows ? response.rows.length : 0}`)
    
    rawResponse.value = response
    
    if (response.code === 200) {
      apiStatus.value = '成功'
      
      if (response.rows && Array.isArray(response.rows)) {
        tools.value = response.rows.map(tool => {
          addLog('info', `处理工具: ${tool.toolName}`)
          
          // 根据工具名称动态映射toolType
          const toolName = tool.toolName || ''
          
          if (toolName.includes('单向阀建模')) {
            tool.toolType = 'jm'
          } else if (toolName.includes('O型圈选型') || toolName.includes('O形圈选型')) {
            tool.toolType = 'o'
          } else if (toolName.includes('单向阀仿真')) {
            tool.toolType = 'fz'
          } else if (toolName.includes('单向阀建模仿真')) {
            tool.toolType = 'fzjm'
          } else {
            tool.toolType = `tool_${tool.toolId}`
          }
          
          return tool
        })
        
        addLog('success', `成功处理 ${tools.value.length} 个工具`)
      } else {
        addLog('warn', 'API响应中没有rows数据或rows不是数组')
        tools.value = []
      }
    } else {
      apiStatus.value = '失败'
      addLog('error', `API返回错误: ${response.msg}`)
      ElMessage.error(response.msg || '获取工具列表失败')
    }
  } catch (error) {
    apiStatus.value = '异常'
    addLog('error', `API请求异常: ${error.message}`)
    console.error('API请求详细错误:', error)
    ElMessage.error('API请求失败')
  }
  
  // 测试类型API
  try {
    addLog('info', '开始测试类型列表API...')
    
    const typesResponse = await getTypesList({
      pageNum: 1,
      pageSize: 100,
      isActive: 1
    })
    
    addLog('info', `类型API响应状态: ${typesResponse.code}`)
    
    if (typesResponse.code === 200) {
      types.value = typesResponse.rows || []
      addLog('success', `成功获取 ${types.value.length} 个类型`)
    } else {
      addLog('error', `类型API返回错误: ${typesResponse.msg}`)
    }
  } catch (error) {
    addLog('error', `类型API请求异常: ${error.message}`)
  }
}

// 页面加载时自动测试
onMounted(() => {
  addLog('info', '页面加载完成，开始自动测试...')
  testApi()
})
</script>

<style scoped>
.debug-container {
  padding: 20px;
}

.debug-card {
  margin-bottom: 20px;
}

.debug-section {
  max-height: 400px;
  overflow-y: auto;
}

.json-display {
  background: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
  font-size: 12px;
  max-height: 200px;
  overflow-y: auto;
}

.tool-card {
  margin-bottom: 10px;
}

.log-item {
  margin-bottom: 5px;
  font-family: monospace;
  font-size: 12px;
}

.log-time {
  color: #666;
  margin-right: 10px;
}

.log-info {
  color: #409eff;
}

.log-success {
  color: #67c23a;
}

.log-warn {
  color: #e6a23c;
}

.log-error {
  color: #f56c6c;
}
</style>
