#!/usr/bin/env python3
"""
权限系统验证脚本
用于快速验证权限系统的配置是否正确
"""

import sys
import os
import asyncio
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from config.permissions import Permissions, RoleKeys, ProjectRoles, get_role_permissions


class PermissionVerifier:
    """权限系统验证器"""

    def __init__(self):
        self.errors = []
        self.warnings = []

    def log_error(self, message: str):
        """记录错误"""
        self.errors.append(f"❌ ERROR: {message}")
        print(f"❌ ERROR: {message}")

    def log_warning(self, message: str):
        """记录警告"""
        self.warnings.append(f"⚠️  WARNING: {message}")
        print(f"⚠️  WARNING: {message}")

    def log_success(self, message: str):
        """记录成功"""
        print(f"✅ SUCCESS: {message}")

    def verify_permission_constants(self):
        """验证权限常量定义"""
        print("\n=== 验证权限常量定义 ===")

        # 验证角色常量
        required_roles = ['admin', 'project_manager', 'project_member', 'common']
        for role in required_roles:
            if hasattr(RoleKeys, role.upper()):
                role_value = getattr(RoleKeys, role.upper())
                if role_value == role:
                    self.log_success(f"角色常量 {role.upper()} 定义正确")
                else:
                    self.log_error(f"角色常量 {role.upper()} 值不正确: 期望 {role}, 实际 {role_value}")
            else:
                self.log_error(f"缺少角色常量定义: {role.upper()}")

        # 验证项目角色常量
        project_roles = ['manager', 'member']
        for role in project_roles:
            if hasattr(ProjectRoles, role.upper()):
                role_value = getattr(ProjectRoles, role.upper())
                if role_value == role:
                    self.log_success(f"项目角色常量 {role.upper()} 定义正确")
                else:
                    self.log_error(f"项目角色常量 {role.upper()} 值不正确: 期望 {role}, 实际 {role_value}")
            else:
                self.log_error(f"缺少项目角色常量定义: {role.upper()}")

        # 验证超级权限常量
        if Permissions.ALL == '*:*:*':
            self.log_success("超级权限常量定义正确")
        else:
            self.log_error(f"超级权限常量值不正确: 期望 '*:*:*', 实际 {Permissions.ALL}")

    def verify_role_permissions(self):
        """验证角色权限配置"""
        print("\n=== 验证角色权限配置 ===")

        # 验证各角色的权限配置
        roles_to_check = [
            RoleKeys.ADMIN,
            RoleKeys.PROJECT_MANAGER,
            RoleKeys.PROJECT_MEMBER,
            RoleKeys.COMMON
        ]

        for role in roles_to_check:
            permissions = get_role_permissions(role)
            if permissions:
                self.log_success(f"角色 {role} 有 {len(permissions)} 个权限")
                
                # 验证超级管理员权限
                if role == RoleKeys.ADMIN:
                    if Permissions.ALL in permissions:
                        self.log_success("超级管理员拥有全部权限")
                    else:
                        self.log_error("超级管理员缺少全部权限标识")
                
                # 验证项目管理员权限
                elif role == RoleKeys.PROJECT_MANAGER:
                    required_perms = [
                        Permissions.Business.PROJECT_LIST,
                        Permissions.Business.PROJECT_ADD,
                        Permissions.Business.TASK_LIST,
                        Permissions.Business.KB_LIST
                    ]
                    for perm in required_perms:
                        if perm in permissions:
                            self.log_success(f"项目管理员拥有权限: {perm}")
                        else:
                            self.log_error(f"项目管理员缺少权限: {perm}")
                
                # 验证项目成员权限
                elif role == RoleKeys.PROJECT_MEMBER:
                    required_perms = [
                        Permissions.Business.PROJECT_LIST,
                        Permissions.Business.TASK_LIST,
                        Permissions.Business.TASK_ADD
                    ]
                    forbidden_perms = [
                        Permissions.Business.PROJECT_ADD,
                        Permissions.Business.KB_LIST
                    ]
                    
                    for perm in required_perms:
                        if perm in permissions:
                            self.log_success(f"项目成员拥有权限: {perm}")
                        else:
                            self.log_error(f"项目成员缺少权限: {perm}")
                    
                    for perm in forbidden_perms:
                        if perm not in permissions:
                            self.log_success(f"项目成员正确地没有权限: {perm}")
                        else:
                            self.log_warning(f"项目成员不应该拥有权限: {perm}")
                
                # 验证普通用户权限
                elif role == RoleKeys.COMMON:
                    required_perms = [
                        Permissions.Business.TOOL_LIST,
                        Permissions.Business.TOOL_USE
                    ]
                    for perm in required_perms:
                        if perm in permissions:
                            self.log_success(f"普通用户拥有权限: {perm}")
                        else:
                            self.log_error(f"普通用户缺少权限: {perm}")
            else:
                self.log_error(f"角色 {role} 没有配置任何权限")

    def verify_file_structure(self):
        """验证文件结构"""
        print("\n=== 验证文件结构 ===")

        required_files = [
            "config/permissions.py",
            "module_admin/aspect/project_auth.py",
            "module_business/entity/do/project_members_do.py",
            "module_business/entity/vo/project_members_vo.py",
            "module_business/dao/project_members_dao.py",
            "module_business/service/project_members_service.py",
            "module_business/controller/project_members_controller.py",
            "sql/create_project_members_table.sql",
            "sql/business_menus.sql",
            "sql/role_menu_permissions.sql",
            "docs/PERMISSION_SYSTEM.md"
        ]

        for file_path in required_files:
            full_path = project_root / file_path
            if full_path.exists():
                self.log_success(f"文件存在: {file_path}")
            else:
                self.log_error(f"文件缺失: {file_path}")

    def verify_frontend_files(self):
        """验证前端文件"""
        print("\n=== 验证前端文件 ===")

        frontend_root = project_root.parent / "ai-platform-frontend"
        if not frontend_root.exists():
            self.log_warning("前端项目目录不存在，跳过前端文件验证")
            return

        required_frontend_files = [
            "src/utils/projectPermission.js",
            "src/utils/routePermission.js",
            "src/composables/useProjectPermission.js",
            "src/mixins/projectPermission.js",
            "src/directive/permission/hasProjectRole.js",
            "src/directive/permission/hasProjectPermi.js"
        ]

        for file_path in required_frontend_files:
            full_path = frontend_root / file_path
            if full_path.exists():
                self.log_success(f"前端文件存在: {file_path}")
            else:
                self.log_error(f"前端文件缺失: {file_path}")

        # 验证现有路由文件
        existing_routes = [
            "src/router/index.js",
            "src/views/tools/index.vue",
            "src/views/projects/board.vue",
            "src/views/knowledge-bases/index.vue"
        ]

        for file_path in existing_routes:
            full_path = frontend_root / file_path
            if full_path.exists():
                self.log_success(f"现有路由文件存在: {file_path}")
            else:
                self.log_warning(f"现有路由文件可能缺失: {file_path}")

    def verify_import_statements(self):
        """验证导入语句"""
        print("\n=== 验证导入语句 ===")

        try:
            from config.permissions import Permissions, RoleKeys, ProjectRoles
            self.log_success("权限常量导入成功")
        except ImportError as e:
            self.log_error(f"权限常量导入失败: {e}")

        try:
            from module_admin.aspect.project_auth import CheckProjectPermission, CheckTaskPermission
            self.log_success("权限验证中间件导入成功")
        except ImportError as e:
            self.log_error(f"权限验证中间件导入失败: {e}")

        try:
            from module_business.service.project_members_service import ProjectMembersService
            self.log_success("项目成员服务导入成功")
        except ImportError as e:
            self.log_error(f"项目成员服务导入失败: {e}")

    def run_verification(self):
        """运行完整验证"""
        print("🔍 开始权限系统验证...")
        print("=" * 60)

        self.verify_permission_constants()
        self.verify_role_permissions()
        self.verify_file_structure()
        self.verify_frontend_files()
        self.verify_import_statements()

        # 输出总结
        print("\n" + "=" * 60)
        print("📊 验证结果总结")
        print("=" * 60)

        if not self.errors and not self.warnings:
            print("🎉 所有验证都通过了！权限系统配置正确。")
            return True
        else:
            if self.errors:
                print(f"❌ 发现 {len(self.errors)} 个错误:")
                for error in self.errors:
                    print(f"   {error}")

            if self.warnings:
                print(f"⚠️  发现 {len(self.warnings)} 个警告:")
                for warning in self.warnings:
                    print(f"   {warning}")

            print(f"\n📋 建议:")
            print("1. 修复所有错误后重新运行验证")
            print("2. 检查警告项目是否符合预期")
            print("3. 运行测试用例进一步验证功能")

            return len(self.errors) == 0


def main():
    """主函数"""
    verifier = PermissionVerifier()
    success = verifier.run_verification()
    
    if success:
        print("\n🚀 权限系统验证完成，可以继续进行功能测试。")
        print("💡 建议运行以下命令进行进一步测试:")
        print("   python tests/test_permission_scenarios.py")
        print("   python -m pytest tests/test_project_permissions.py -v")
        return 0
    else:
        print("\n🛠️  请修复上述问题后重新验证。")
        return 1


if __name__ == "__main__":
    exit(main())
