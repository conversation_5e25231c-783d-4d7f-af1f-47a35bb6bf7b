-- 创建 rd_tools 和 rd_types 表
-- 执行此脚本来修复工具广场显示问题

-- ==========================================
-- 1. 创建 rd_types 表（通用类型表）
-- ==========================================
DROP TABLE IF EXISTS `rd_types`;
CREATE TABLE `rd_types` (
  `type_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '类型ID',
  `type_name` varchar(100) NOT NULL COMMENT '类型名称',
  `display_name` varchar(100) NOT NULL COMMENT '显示名称',
  `description` text COMMENT '类型描述',
  `is_active` tinyint(1) DEFAULT 1 COMMENT '是否启用（1启用 0禁用）',
  `sort_order` int(11) DEFAULT 0 COMMENT '排序顺序',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `created_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `updated_by` varchar(64) DEFAULT '' COMMENT '更新者',
  PRIMARY KEY (`type_id`),
  UNIQUE KEY `uk_type_name` (`type_name`),
  KEY `idx_is_active` (`is_active`),
  KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB AUTO_INCREMENT=1000 DEFAULT CHARSET=utf8mb4 COMMENT='通用类型表';

-- ==========================================
-- 2. 创建 rd_tools 表（工具表）
-- ==========================================
DROP TABLE IF EXISTS `rd_tools`;
CREATE TABLE `rd_tools` (
  `tool_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '工具ID',
  `tool_name` varchar(100) NOT NULL COMMENT '工具名称',
  `description` text COMMENT '工具描述',
  `vendor` varchar(100) DEFAULT '' COMMENT '供应商',
  `version` varchar(50) DEFAULT '' COMMENT '版本号',
  `type_id` bigint(20) DEFAULT NULL COMMENT '关联类型ID',
  `is_health` tinyint(1) DEFAULT 1 COMMENT '健康状态（1健康 0异常）',
  `queue_required` tinyint(1) DEFAULT 0 COMMENT '是否需要队列（1需要 0不需要）',
  `tool_url` varchar(500) DEFAULT '' COMMENT '工具链接',
  `api_endpoint` varchar(500) DEFAULT '' COMMENT 'API端点',
  `config_json` text COMMENT '配置信息（JSON格式）',
  `is_active` tinyint(1) DEFAULT 1 COMMENT '是否启用（1启用 0禁用）',
  `sort_order` int(11) DEFAULT 0 COMMENT '排序顺序',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `created_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `updated_by` varchar(64) DEFAULT '' COMMENT '更新者',
  PRIMARY KEY (`tool_id`),
  KEY `idx_type_id` (`type_id`),
  KEY `idx_is_active` (`is_active`),
  KEY `idx_is_health` (`is_health`),
  KEY `idx_sort_order` (`sort_order`),
  CONSTRAINT `fk_tools_type_id` FOREIGN KEY (`type_id`) REFERENCES `rd_types` (`type_id`) ON DELETE SET NULL
) ENGINE=InnoDB AUTO_INCREMENT=1000 DEFAULT CHARSET=utf8mb4 COMMENT='工具表';

-- ==========================================
-- 3. 插入示例数据
-- ==========================================

-- 插入类型数据
INSERT INTO `rd_types` (`type_id`, `type_name`, `display_name`, `description`, `is_active`, `sort_order`, `created_by`) VALUES
(1000, 'modeling', '建模工具', '用于建模相关的工具', 1, 1, 'admin'),
(1001, 'simulation', '仿真工具', '用于仿真分析的工具', 1, 2, 'admin'),
(1002, 'selection', '选型工具', '用于零件选型的工具', 1, 3, 'admin'),
(1003, 'analysis', '分析工具', '用于数据分析的工具', 1, 4, 'admin'),
(1004, 'design', '设计工具', '用于设计辅助的工具', 1, 5, 'admin');

-- 插入工具数据
INSERT INTO `rd_tools` (`tool_id`, `tool_name`, `description`, `vendor`, `version`, `type_id`, `is_health`, `queue_required`, `is_active`, `sort_order`, `created_by`) VALUES
(1000, '单向阀建模工具', '用于单向阀三维建模的专业工具，支持参数化建模和多种标准规格', '三华科技', 'v1.2.0', 1000, 1, 0, 1, 1, 'admin'),
(1001, '单向阀仿真工具', '提供单向阀流体仿真分析，包括压力损失、流量特性等关键参数分析', '三华科技', 'v1.1.5', 1001, 1, 1, 1, 2, 'admin'),
(1002, 'O形圈选型工具', '智能O形圈选型助手，根据工况条件推荐合适的O形圈规格和材料', '三华科技', 'v2.0.1', 1002, 1, 0, 1, 3, 'admin'),
(1003, '液压分析工具', '液压系统性能分析工具，支持系统建模和性能预测', '三华科技', 'v1.0.8', 1003, 1, 0, 1, 4, 'admin'),
(1004, '密封设计工具', '密封结构设计助手，提供密封槽设计和密封性能评估', '三华科技', 'v1.3.2', 1004, 0, 0, 1, 5, 'admin');

-- ==========================================
-- 4. 验证创建结果
-- ==========================================
SELECT '=== 类型表数据 ===' as section;
SELECT type_id, type_name, display_name, is_active FROM rd_types ORDER BY sort_order;

SELECT '=== 工具表数据 ===' as section;
SELECT t.tool_id, t.tool_name, t.vendor, t.version, ty.display_name as type_name, t.is_health, t.is_active 
FROM rd_tools t
LEFT JOIN rd_types ty ON t.type_id = ty.type_id
ORDER BY t.sort_order;

SELECT '=== 表创建完成 ===' as message;