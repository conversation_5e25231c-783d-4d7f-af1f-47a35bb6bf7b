-- 权限管理功能重构 - 简化版角色配置
-- 执行时间: 2024年
-- 描述: 新增项目管理员和项目成员角色，基于现有菜单配置权限

-- 1. 新增角色
INSERT INTO sys_role (role_name, role_key, role_sort, data_scope, menu_check_strictly, dept_check_strictly, status, del_flag, create_by, create_time, remark) VALUES
('项目管理员', 'project_manager', 3, '1', 1, 1, '0', '0', 'admin', NOW(), '项目管理员角色，负责项目全生命周期管理'),
('项目成员', 'project_member', 4, '1', 1, 1, '0', '0', 'admin', NOW(), '项目成员角色，可查看项目任务并创建自己的任务');

-- 2. 为项目管理员配置权限 (假设新角色ID为3)
-- 项目管理员拥有除系统管理外的大部分权限
INSERT INTO sys_role_menu (role_id, menu_id) 
SELECT 3, menu_id FROM sys_menu 
WHERE menu_id IN (
    -- 首页
    1,
    -- 如果有业务相关菜单，可以在这里添加
    -- 例如：项目管理、任务管理、数据库管理等
    2, 3, 4
) AND status = '0';

-- 3. 为项目成员配置权限 (假设新角色ID为4)
-- 项目成员只有基础查看权限
INSERT INTO sys_role_menu (role_id, menu_id) 
SELECT 4, menu_id FROM sys_menu 
WHERE menu_id IN (
    -- 首页
    1,
    -- 基础查看权限
    2, 3
) AND status = '0';

-- 4. 创建角色权限验证的辅助函数（可选）
-- 这些可以在应用层实现，这里仅作为参考

-- 查询用户角色的SQL示例
/*
SELECT DISTINCT r.role_key 
FROM sys_user u
JOIN sys_user_role ur ON u.user_id = ur.user_id
JOIN sys_role r ON ur.role_id = r.role_id
WHERE u.user_id = ? AND u.status = '0' AND r.status = '0';
*/

-- 查询用户权限的SQL示例
/*
SELECT DISTINCT m.perms
FROM sys_user u
JOIN sys_user_role ur ON u.user_id = ur.user_id
JOIN sys_role r ON ur.role_id = r.role_id
JOIN sys_role_menu rm ON r.role_id = rm.role_id
JOIN sys_menu m ON rm.menu_id = m.menu_id
WHERE u.user_id = ? AND u.status = '0' AND r.status = '0' AND m.status = '0' AND m.perms IS NOT NULL AND m.perms != '';
*/

-- 5. 更新现有普通角色权限（可选）
-- 如果需要限制普通用户的权限，可以清空并重新配置
/*
DELETE FROM sys_role_menu WHERE role_id = 2;
INSERT INTO sys_role_menu (role_id, menu_id) VALUES (2, 1); -- 仅首页权限
*/

-- 6. 示例：为测试用户分配新角色
-- INSERT INTO sys_user_role (user_id, role_id) VALUES (2, 3); -- 用户2设为项目管理员
-- INSERT INTO sys_user_role (user_id, role_id) VALUES (3, 4); -- 用户3设为项目成员（如果存在）

-- 注意事项：
-- 1. 执行前请备份数据库
-- 2. 角色ID可能因数据库中现有数据而不同，请根据实际情况调整
-- 3. 菜单ID需要根据实际的菜单结构进行配置
-- 4. 建议分步执行，先创建角色，再配置权限
