import hasRole from './hasRole'
import hasPermi from './hasPermi'
import hasProjectRole from './hasProjectRole'
import hasProjectPermi from './hasProjectPermi'

const install = function(Vue) {
  Vue.directive('hasRole', hasRole)
  Vue.directive('hasPermi', hasPermi)
  Vue.directive('hasProjectRole', hasProjectRole)
  Vue.directive('hasProjectPermi', hasProjectPermi)
}

if (window.Vue) {
  window['hasRole'] = hasRole
  window['hasPermi'] = hasPermi
  window['hasProjectRole'] = hasProjectRole
  window['hasProjectPermi'] = hasProjectPermi
  Vue.use(install); // eslint-disable-line
}

export default install
