-- 权限系统回滚SQL脚本
-- 用于撤销权限系统的所有更改
-- 执行时间: 2024年

-- ==========================================
-- 警告：此脚本将删除所有权限系统相关数据
-- 请在执行前备份数据库！
-- ==========================================

-- 1. 删除项目成员关联表
DROP TABLE IF EXISTS `rd_project_members`;

-- 2. 删除项目相关角色
DELETE FROM `sys_user_role` WHERE `role_id` IN (100, 101);
DELETE FROM `sys_role_menu` WHERE `role_id` IN (100, 101);
DELETE FROM `sys_role` WHERE `role_id` IN (100, 101);

-- 3. 删除添加的功能按钮权限
DELETE FROM `sys_menu` WHERE `perms` IN (
    'business:projects:query',
    'business:projects:add', 
    'business:projects:edit',
    'business:projects:remove',
    'business:tasks:query',
    'business:tasks:add',
    'business:tasks:edit', 
    'business:tasks:remove',
    'business:knowledge-bases:query',
    'business:knowledge-bases:add',
    'business:knowledge-bases:edit',
    'business:knowledge-bases:remove',
    'business:tools:query',
    'business:tools:use'
) AND `menu_type` = 'F';

-- 4. 清除菜单权限标识（可选，如果不想保留）
-- UPDATE `sys_menu` SET `perms` = NULL WHERE `path` IN ('tools', 'projects', 'knowledge-bases');

-- 5. 恢复普通用户的原始权限（如果需要）
-- 这里需要根据实际情况调整，恢复普通用户原来的菜单权限

COMMIT;

-- 验证回滚结果
SELECT '回滚完成，请检查以下查询结果：' as message;
SELECT COUNT(*) as project_members_count FROM information_schema.tables WHERE table_name = 'rd_project_members';
SELECT COUNT(*) as project_roles_count FROM sys_role WHERE role_key IN ('project_manager', 'project_member');
SELECT COUNT(*) as permission_buttons_count FROM sys_menu WHERE perms LIKE 'business:%' AND menu_type = 'F';
