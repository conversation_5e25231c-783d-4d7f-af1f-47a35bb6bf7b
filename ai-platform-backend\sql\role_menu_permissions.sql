-- 角色菜单权限配置SQL（适配现有菜单结构）
-- 执行时间: 2024年
-- 描述: 为不同角色配置现有菜单的访问权限

-- 注意：此脚本基于现有菜单结构配置权限，不创建新菜单
-- 现有菜单：首页、工具广场、项目看板、数据库管理

-- 假设角色ID分配如下：
-- 1: admin (超级管理员)
-- 2: common (普通用户)
-- 3: project_manager (项目管理员)
-- 4: project_member (项目成员)

-- 获取现有菜单ID（用于权限配置）
-- 首页菜单ID通常为固定值
-- 工具广场、项目看板、数据库管理菜单需要根据实际情况获取

-- 1. 超级管理员 (admin) - 拥有所有权限
-- 超级管理员默认拥有所有菜单权限，无需额外配置

-- 2. 项目管理员 (project_manager) - 拥有工具广场、项目看板、数据库管理权限
-- 为项目管理员分配现有菜单的访问权限
INSERT INTO sys_role_menu (role_id, menu_id)
SELECT 3, menu_id FROM sys_menu WHERE path IN ('tools', 'projects', 'knowledge-bases') AND status = '0';

-- 如果需要为项目管理员分配首页权限
INSERT INTO sys_role_menu (role_id, menu_id)
SELECT 3, menu_id FROM sys_menu WHERE path = 'index' AND status = '0';

-- 3. 项目成员 (project_member) - 拥有工具广场、项目看板权限
-- 为项目成员分配工具广场和项目看板的访问权限
INSERT INTO sys_role_menu (role_id, menu_id)
SELECT 4, menu_id FROM sys_menu WHERE path IN ('tools', 'projects') AND status = '0';

-- 如果需要为项目成员分配首页权限
INSERT INTO sys_role_menu (role_id, menu_id)
SELECT 4, menu_id FROM sys_menu WHERE path = 'index' AND status = '0';

-- 4. 普通用户 (common) - 仅拥有工具广场权限
-- 为普通用户分配工具广场的访问权限
INSERT INTO sys_role_menu (role_id, menu_id)
SELECT 2, menu_id FROM sys_menu WHERE path IN ('tools') AND status = '0';

-- 如果需要为普通用户分配首页权限
INSERT INTO sys_role_menu (role_id, menu_id)
SELECT 2, menu_id FROM sys_menu WHERE path = 'index' AND status = '0';

-- 验证配置的SQL查询（可选执行）
/*
-- 查看各角色的菜单权限
SELECT 
    r.role_name,
    r.role_key,
    m.menu_name,
    m.path,
    m.perms,
    m.menu_type
FROM sys_role r
JOIN sys_role_menu rm ON r.role_id = rm.role_id
JOIN sys_menu m ON rm.menu_id = m.menu_id
WHERE r.role_id IN (2, 3, 4) AND m.menu_id >= 2000
ORDER BY r.role_id, m.menu_id;

-- 查看项目管理员的权限
SELECT 
    m.menu_name,
    m.path,
    m.perms,
    m.menu_type
FROM sys_role_menu rm
JOIN sys_menu m ON rm.menu_id = m.menu_id
WHERE rm.role_id = 3 AND m.menu_id >= 2000
ORDER BY m.menu_id;

-- 查看项目成员的权限
SELECT 
    m.menu_name,
    m.path,
    m.perms,
    m.menu_type
FROM sys_role_menu rm
JOIN sys_menu m ON rm.menu_id = m.menu_id
WHERE rm.role_id = 4 AND m.menu_id >= 2000
ORDER BY m.menu_id;

-- 查看普通用户的权限
SELECT 
    m.menu_name,
    m.path,
    m.perms,
    m.menu_type
FROM sys_role_menu rm
JOIN sys_menu m ON rm.menu_id = m.menu_id
WHERE rm.role_id = 2 AND m.menu_id >= 2000
ORDER BY m.menu_id;
*/

-- 注意事项：
-- 1. 角色ID需要根据实际数据库中的角色ID进行调整
-- 2. 菜单ID需要根据实际插入的菜单ID进行调整
-- 3. 执行前请确认角色和菜单都已正确创建
-- 4. 可以根据实际业务需求调整权限配置
-- 5. 建议在测试环境先验证权限配置的正确性
