"""
权限系统场景测试
测试实际业务场景中的权限控制
"""

import asyncio
from typing import List, Dict, Any
from dataclasses import dataclass
from enum import Enum


class UserRole(Enum):
    """用户角色枚举"""
    ADMIN = "admin"
    PROJECT_MANAGER = "project_manager"
    PROJECT_MEMBER = "project_member"
    COMMON = "common"


class ActionType(Enum):
    """操作类型枚举"""
    VIEW = "view"
    CREATE = "create"
    EDIT = "edit"
    DELETE = "delete"
    MANAGE = "manage"


@dataclass
class TestUser:
    """测试用户"""
    user_id: int
    username: str
    role: UserRole
    permissions: List[str]


@dataclass
class TestProject:
    """测试项目"""
    project_id: int
    name: str
    owner_id: int
    members: List[int]  # 项目成员ID列表


@dataclass
class TestTask:
    """测试任务"""
    task_id: int
    name: str
    project_id: int
    creator_id: int


@dataclass
class PermissionTestCase:
    """权限测试用例"""
    name: str
    user: TestUser
    target: Any  # 目标对象（项目、任务等）
    action: ActionType
    expected_result: bool
    description: str


class PermissionTestRunner:
    """权限测试运行器"""

    def __init__(self):
        self.test_cases: List[PermissionTestCase] = []
        self.setup_test_data()

    def setup_test_data(self):
        """设置测试数据"""
        # 创建测试用户
        self.admin = TestUser(
            user_id=1,
            username="admin",
            role=UserRole.ADMIN,
            permissions=["*:*:*"]
        )

        self.project_manager = TestUser(
            user_id=2,
            username="project_manager",
            role=UserRole.PROJECT_MANAGER,
            permissions=[
                "business:projects:list",
                "business:projects:add",
                "business:projects:edit",
                "business:tasks:list",
                "business:tasks:add",
                "business:tasks:edit",
                "business:knowledge-bases:list"
            ]
        )

        self.project_member = TestUser(
            user_id=3,
            username="project_member",
            role=UserRole.PROJECT_MEMBER,
            permissions=[
                "business:projects:list",
                "business:tasks:list",
                "business:tasks:add"
            ]
        )

        self.common_user = TestUser(
            user_id=4,
            username="common_user",
            role=UserRole.COMMON,
            permissions=[
                "business:tools:list",
                "business:tools:use"
            ]
        )

        # 创建测试项目
        self.project1 = TestProject(
            project_id=1,
            name="项目1",
            owner_id=2,  # 项目管理员拥有
            members=[2, 3]  # 项目管理员和项目成员
        )

        self.project2 = TestProject(
            project_id=2,
            name="项目2",
            owner_id=1,  # 超级管理员拥有
            members=[1]  # 只有超级管理员
        )

        # 创建测试任务
        self.task1 = TestTask(
            task_id=1,
            name="任务1",
            project_id=1,
            creator_id=3  # 项目成员创建
        )

        self.task2 = TestTask(
            task_id=2,
            name="任务2",
            project_id=1,
            creator_id=2  # 项目管理员创建
        )

    def add_test_case(self, test_case: PermissionTestCase):
        """添加测试用例"""
        self.test_cases.append(test_case)

    def check_permission(self, user: TestUser, target: Any, action: ActionType) -> bool:
        """检查权限（模拟实际权限检查逻辑）"""
        # 超级管理员拥有所有权限
        if user.role == UserRole.ADMIN:
            return True

        # 根据目标类型和操作类型检查权限
        if isinstance(target, TestProject):
            return self._check_project_permission(user, target, action)
        elif isinstance(target, TestTask):
            return self._check_task_permission(user, target, action)
        else:
            return False

    def _check_project_permission(self, user: TestUser, project: TestProject, action: ActionType) -> bool:
        """检查项目权限"""
        if action == ActionType.VIEW:
            # 项目成员及以上可以查看项目
            if user.role in [UserRole.PROJECT_MANAGER, UserRole.PROJECT_MEMBER]:
                return user.user_id in project.members
            return False

        elif action in [ActionType.EDIT, ActionType.DELETE, ActionType.MANAGE]:
            # 项目所有者或项目管理员可以编辑/删除/管理项目
            if user.user_id == project.owner_id:
                return True
            if user.role == UserRole.PROJECT_MANAGER and user.user_id in project.members:
                return True
            return False

        elif action == ActionType.CREATE:
            # 项目管理员及以上可以创建项目
            return user.role in [UserRole.PROJECT_MANAGER]

        return False

    def _check_task_permission(self, user: TestUser, task: TestTask, action: ActionType) -> bool:
        """检查任务权限"""
        # 获取任务所属项目
        project = None
        if task.project_id == 1:
            project = self.project1
        elif task.project_id == 2:
            project = self.project2

        if not project:
            return False

        if action == ActionType.VIEW:
            # 项目成员可以查看任务
            return user.user_id in project.members

        elif action == ActionType.CREATE:
            # 项目成员可以创建任务
            return user.user_id in project.members and user.role in [UserRole.PROJECT_MANAGER, UserRole.PROJECT_MEMBER]

        elif action in [ActionType.EDIT, ActionType.DELETE]:
            # 任务创建者或项目管理员可以编辑/删除任务
            if user.user_id == task.creator_id:
                return True
            if user.role == UserRole.PROJECT_MANAGER and user.user_id in project.members:
                return True
            if user.user_id == project.owner_id:
                return True
            return False

        return False

    def create_test_scenarios(self):
        """创建测试场景"""
        # 项目权限测试场景
        project_scenarios = [
            # 超级管理员场景
            PermissionTestCase(
                name="超级管理员查看项目",
                user=self.admin,
                target=self.project1,
                action=ActionType.VIEW,
                expected_result=True,
                description="超级管理员应该能查看所有项目"
            ),
            PermissionTestCase(
                name="超级管理员编辑项目",
                user=self.admin,
                target=self.project1,
                action=ActionType.EDIT,
                expected_result=True,
                description="超级管理员应该能编辑所有项目"
            ),

            # 项目管理员场景
            PermissionTestCase(
                name="项目管理员查看自己的项目",
                user=self.project_manager,
                target=self.project1,
                action=ActionType.VIEW,
                expected_result=True,
                description="项目管理员应该能查看自己参与的项目"
            ),
            PermissionTestCase(
                name="项目管理员编辑自己的项目",
                user=self.project_manager,
                target=self.project1,
                action=ActionType.EDIT,
                expected_result=True,
                description="项目管理员应该能编辑自己拥有的项目"
            ),
            PermissionTestCase(
                name="项目管理员查看其他项目",
                user=self.project_manager,
                target=self.project2,
                action=ActionType.VIEW,
                expected_result=False,
                description="项目管理员不应该能查看不参与的项目"
            ),

            # 项目成员场景
            PermissionTestCase(
                name="项目成员查看参与的项目",
                user=self.project_member,
                target=self.project1,
                action=ActionType.VIEW,
                expected_result=True,
                description="项目成员应该能查看参与的项目"
            ),
            PermissionTestCase(
                name="项目成员编辑项目",
                user=self.project_member,
                target=self.project1,
                action=ActionType.EDIT,
                expected_result=False,
                description="项目成员不应该能编辑项目"
            ),

            # 普通用户场景
            PermissionTestCase(
                name="普通用户查看项目",
                user=self.common_user,
                target=self.project1,
                action=ActionType.VIEW,
                expected_result=False,
                description="普通用户不应该能查看任何项目"
            ),
        ]

        # 任务权限测试场景
        task_scenarios = [
            # 任务创建者场景
            PermissionTestCase(
                name="任务创建者编辑自己的任务",
                user=self.project_member,
                target=self.task1,
                action=ActionType.EDIT,
                expected_result=True,
                description="任务创建者应该能编辑自己创建的任务"
            ),
            PermissionTestCase(
                name="任务创建者删除自己的任务",
                user=self.project_member,
                target=self.task1,
                action=ActionType.DELETE,
                expected_result=True,
                description="任务创建者应该能删除自己创建的任务"
            ),

            # 项目管理员场景
            PermissionTestCase(
                name="项目管理员编辑项目内任务",
                user=self.project_manager,
                target=self.task1,
                action=ActionType.EDIT,
                expected_result=True,
                description="项目管理员应该能编辑项目内的任务"
            ),

            # 非任务创建者场景
            PermissionTestCase(
                name="非创建者编辑任务",
                user=self.project_member,
                target=self.task2,
                action=ActionType.EDIT,
                expected_result=False,
                description="非任务创建者不应该能编辑其他人的任务"
            ),
        ]

        self.test_cases.extend(project_scenarios)
        self.test_cases.extend(task_scenarios)

    def run_tests(self) -> Dict[str, Any]:
        """运行所有测试"""
        self.create_test_scenarios()
        
        results = {
            "total": len(self.test_cases),
            "passed": 0,
            "failed": 0,
            "failures": []
        }

        print(f"开始运行 {len(self.test_cases)} 个权限测试用例...\n")

        for i, test_case in enumerate(self.test_cases, 1):
            actual_result = self.check_permission(
                test_case.user,
                test_case.target,
                test_case.action
            )

            if actual_result == test_case.expected_result:
                results["passed"] += 1
                status = "✅ PASS"
            else:
                results["failed"] += 1
                status = "❌ FAIL"
                results["failures"].append({
                    "name": test_case.name,
                    "expected": test_case.expected_result,
                    "actual": actual_result,
                    "description": test_case.description
                })

            print(f"{i:2d}. {status} - {test_case.name}")
            print(f"    描述: {test_case.description}")
            print(f"    用户: {test_case.user.username} ({test_case.user.role.value})")
            print(f"    操作: {test_case.action.value}")
            print(f"    期望: {test_case.expected_result}, 实际: {actual_result}")
            print()

        return results

    def print_summary(self, results: Dict[str, Any]):
        """打印测试摘要"""
        print("=" * 60)
        print("权限系统测试摘要")
        print("=" * 60)
        print(f"总测试用例: {results['total']}")
        print(f"通过: {results['passed']}")
        print(f"失败: {results['failed']}")
        print(f"成功率: {results['passed'] / results['total'] * 100:.1f}%")

        if results["failures"]:
            print("\n失败的测试用例:")
            for failure in results["failures"]:
                print(f"- {failure['name']}")
                print(f"  期望: {failure['expected']}, 实际: {failure['actual']}")
                print(f"  描述: {failure['description']}")


def main():
    """主函数"""
    runner = PermissionTestRunner()
    results = runner.run_tests()
    runner.print_summary(results)
    
    # 返回退出码
    return 0 if results["failed"] == 0 else 1


if __name__ == "__main__":
    exit(main())
