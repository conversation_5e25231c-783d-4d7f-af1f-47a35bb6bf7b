-- 修复菜单路由问题的SQL脚本
-- 解决项目看板404和数据库管理重复菜单问题
-- 执行时间: 2024年

-- ==========================================
-- 1. 查看当前菜单配置
-- ==========================================
SELECT '=== 当前菜单配置 ===' as section;
SELECT menu_id, menu_name, parent_id, path, component, menu_type, visible, status 
FROM sys_menu 
WHERE path IN ('projects', 'knowledge-bases', 'database', 'tools') 
   OR menu_name LIKE '%项目%' 
   OR menu_name LIKE '%数据库%'
   OR menu_name LIKE '%看板%'
ORDER BY parent_id, order_num;

-- ==========================================
-- 2. 删除重复和错误的菜单项
-- ==========================================

-- 删除可能存在的重复项目管理菜单
DELETE FROM sys_menu WHERE menu_name = '项目管理' AND path != 'projects';
DELETE FROM sys_menu WHERE menu_name = '项目列表' AND parent_id != (SELECT menu_id FROM (SELECT menu_id FROM sys_menu WHERE path = 'projects' LIMIT 1) as temp);

-- 删除可能存在的重复数据库管理菜单
DELETE FROM sys_menu WHERE menu_name = '数据库管理' AND path = 'database';
DELETE FROM sys_menu WHERE path = 'database/knowledge-bases';

-- 删除可能存在的子菜单项（保持扁平结构）
DELETE FROM sys_menu WHERE parent_id IN (
    SELECT menu_id FROM (
        SELECT menu_id FROM sys_menu WHERE path IN ('projects', 'knowledge-bases') AND menu_type = 'M'
    ) as temp
) AND menu_type = 'C';

-- ==========================================
-- 3. 确保正确的菜单配置
-- ==========================================

-- 更新或插入工具广场菜单
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, menu_type, visible, status, perms, icon, create_by, create_time) 
VALUES ('工具广场', 0, 1, 'tools', NULL, 'M', '0', '0', 'business:tools:list', 'tools', 'admin', NOW())
ON DUPLICATE KEY UPDATE 
    menu_name = '工具广场',
    path = 'tools',
    perms = 'business:tools:list',
    icon = 'tools',
    menu_type = 'M',
    visible = '0',
    status = '0';

-- 更新或插入项目看板菜单（扁平结构，无子菜单）
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, menu_type, visible, status, perms, icon, create_by, create_time) 
VALUES ('项目看板', 0, 2, 'projects', NULL, 'M', '0', '0', 'business:projects:list', 'kanban', 'admin', NOW())
ON DUPLICATE KEY UPDATE 
    menu_name = '项目看板',
    path = 'projects',
    perms = 'business:projects:list',
    icon = 'kanban',
    menu_type = 'M',
    visible = '0',
    status = '0';

-- 更新或插入数据库管理菜单（扁平结构，无子菜单）
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, menu_type, visible, status, perms, icon, create_by, create_time) 
VALUES ('数据库管理', 0, 3, 'knowledge-bases', NULL, 'M', '0', '0', 'business:knowledge-bases:list', 'database', 'admin', NOW())
ON DUPLICATE KEY UPDATE 
    menu_name = '数据库管理',
    path = 'knowledge-bases',
    perms = 'business:knowledge-bases:list',
    icon = 'database',
    menu_type = 'M',
    visible = '0',
    status = '0';

-- ==========================================
-- 4. 清理角色菜单关联中的无效菜单ID
-- ==========================================

-- 删除指向不存在菜单的角色菜单关联
DELETE rm FROM sys_role_menu rm 
LEFT JOIN sys_menu m ON rm.menu_id = m.menu_id 
WHERE m.menu_id IS NULL;

-- ==========================================
-- 5. 重新配置角色菜单权限
-- ==========================================

-- 获取正确的菜单ID
SET @tools_menu_id = (SELECT menu_id FROM sys_menu WHERE path = 'tools' AND parent_id = 0 LIMIT 1);
SET @projects_menu_id = (SELECT menu_id FROM sys_menu WHERE path = 'projects' AND parent_id = 0 LIMIT 1);
SET @kb_menu_id = (SELECT menu_id FROM sys_menu WHERE path = 'knowledge-bases' AND parent_id = 0 LIMIT 1);
SET @index_menu_id = (SELECT menu_id FROM sys_menu WHERE (path = 'index' OR path = '') AND parent_id = 0 LIMIT 1);

-- 项目管理员权限 (role_id = 100)
DELETE FROM sys_role_menu WHERE role_id = 100;
INSERT IGNORE INTO sys_role_menu (role_id, menu_id) VALUES
(100, @index_menu_id),
(100, @tools_menu_id),
(100, @projects_menu_id),
(100, @kb_menu_id);

-- 项目成员权限 (role_id = 101)  
DELETE FROM sys_role_menu WHERE role_id = 101;
INSERT IGNORE INTO sys_role_menu (role_id, menu_id) VALUES
(101, @index_menu_id),
(101, @tools_menu_id),
(101, @projects_menu_id);

-- 普通用户权限 (role_id = 2)
DELETE FROM sys_role_menu WHERE role_id = 2;
INSERT IGNORE INTO sys_role_menu (role_id, menu_id) VALUES
(2, @index_menu_id),
(2, @tools_menu_id);

-- ==========================================
-- 6. 验证修复结果
-- ==========================================
SELECT '=== 修复后的菜单配置 ===' as section;
SELECT menu_id, menu_name, parent_id, path, component, menu_type, visible, status, perms, icon
FROM sys_menu 
WHERE parent_id = 0 AND menu_type = 'M' AND status = '0'
ORDER BY order_num;

SELECT '=== 角色菜单权限配置 ===' as section;
SELECT r.role_name, m.menu_name, m.path, m.perms
FROM sys_role r
JOIN sys_role_menu rm ON r.role_id = rm.role_id
JOIN sys_menu m ON rm.menu_id = m.menu_id
WHERE r.role_id IN (2, 100, 101) AND m.status = '0'
ORDER BY r.role_id, m.order_num;

-- ==========================================
-- 7. 清理缓存（如果使用Redis缓存）
-- ==========================================
-- 注意：如果系统使用了菜单缓存，需要清理缓存
-- 这部分需要在应用层面处理，或者重启应用服务

COMMIT;
