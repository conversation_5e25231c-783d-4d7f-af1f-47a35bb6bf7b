import { checkRole } from '@/utils/permission'
import { isAdmin, isProjectManager, isProjectMember } from '@/utils/projectPermission'

export default {
  mounted(el, binding, vnode) {
    const { value } = binding

    if (value && value instanceof Array && value.length > 0) {
      const requiredRoles = value
      let hasRole = false

      // 检查项目角色权限
      if (requiredRoles.includes('project_manager')) {
        hasRole = isProjectManager()
      } else if (requiredRoles.includes('project_member')) {
        hasRole = isProjectMember()
      } else if (requiredRoles.includes('admin')) {
        hasRole = isAdmin()
      } else {
        // 使用通用角色检查
        hasRole = checkRole(requiredRoles)
      }

      if (!hasRole) {
        el.parentNode && el.parentNode.removeChild(el)
      }
    } else {
      throw new Error(`need roles! Like v-hasProjectRole="['project_manager','project_member']"`)
    }
  }
}
