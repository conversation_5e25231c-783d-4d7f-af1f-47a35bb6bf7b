<template>
  <div style="padding: 20px;">
    <h1>工具广场测试页面</h1>
    <p>如果你能看到这个页面，说明路由是正常的</p>
    
    <el-button @click="testApi">测试API</el-button>
    
    <div v-if="tools.length > 0" style="margin-top: 20px;">
      <h2>工具列表 ({{ tools.length }} 个):</h2>
      <ul>
        <li v-for="tool in tools" :key="tool.toolId">
          {{ tool.toolName }} - {{ tool.description }}
        </li>
      </ul>
    </div>
    
    <div v-if="error" style="color: red; margin-top: 20px;">
      错误: {{ error }}
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { getToolsList } from '@/api/tools'
import { ElMessage } from 'element-plus'

const tools = ref([])
const error = ref('')

const testApi = async () => {
  try {
    console.log('测试API调用...')
    const response = await getToolsList({ pageNum: 1, pageSize: 10 })
    console.log('API响应:', response)
    
    if (response.code === 200) {
      tools.value = response.rows || []
      ElMessage.success(`获取到 ${tools.value.length} 个工具`)
    } else {
      error.value = response.msg || '获取失败'
      ElMessage.error(error.value)
    }
  } catch (err) {
    error.value = err.message
    console.error('API调用失败:', err)
    ElMessage.error('API调用失败: ' + err.message)
  }
}
</script>
