# 权限系统部署指南（适配现有菜单结构）

## 概述

本指南说明如何在现有系统中部署权限管理功能，不会新增菜单栏，只对现有的"项目看板"和"数据库管理"菜单进行权限控制。

## 部署步骤

### 1. 后端部署

#### 1.1 创建项目成员表
```bash
# 执行项目成员表创建脚本
mysql -u username -p database_name < sql/create_project_members_table.sql
```

#### 1.2 配置角色权限
```bash
# 创建项目相关角色
mysql -u username -p database_name < sql/setup_project_roles.sql

# 配置角色菜单权限（适配现有菜单）
mysql -u username -p database_name < sql/role_menu_permissions.sql
```

#### 1.3 注册项目成员控制器
在 `server.py` 中添加：
```python
from module_business.controller.project_members_controller import projectMembersController

# 在controller_list中添加
{'router': projectMembersController, 'tags': ['项目成员管理']},
```

### 2. 前端部署

#### 2.1 注册权限指令
在 `main.js` 中添加：
```javascript
import permission from '@/directive/permission'
app.use(permission)
```

#### 2.2 在路由守卫中添加权限检查
在 `permission.js` 中添加：
```javascript
import { checkRoutePermission } from '@/utils/routePermission'

// 在路由守卫中使用
router.beforeEach((to, from, next) => {
  // 现有逻辑...
  
  // 添加权限检查
  if (!checkRoutePermission(to)) {
    next('/401')
    return
  }
  
  next()
})
```

### 3. 权限配置

#### 3.1 角色分配
```sql
-- 为用户分配项目管理员角色
INSERT INTO sys_user_role (user_id, role_id) VALUES (2, 3);

-- 为用户分配项目成员角色
INSERT INTO sys_user_role (user_id, role_id) VALUES (3, 4);
```

#### 3.2 项目成员管理
```sql
-- 添加项目成员
INSERT INTO rd_project_members (project_id, user_id, role, assigned_at, assigned_by) 
VALUES (1, 2, 'manager', NOW(), 1);

INSERT INTO rd_project_members (project_id, user_id, role, assigned_at, assigned_by) 
VALUES (1, 3, 'member', NOW(), 1);
```

## 权限验证

### 运行验证脚本
```bash
# 验证权限系统配置
python scripts/verify_permissions.py

# 运行权限测试
python tests/test_permission_scenarios.py
```

### 手动验证步骤

1. **普通用户验证**
   - 登录普通用户账号
   - 确认只能访问首页和工具广场
   - 尝试访问项目看板，应该被拒绝

2. **项目成员验证**
   - 登录项目成员账号
   - 确认可以访问首页、工具广场、项目看板
   - 确认不能访问数据库管理
   - 确认只能编辑自己创建的任务

3. **项目管理员验证**
   - 登录项目管理员账号
   - 确认可以访问所有菜单（除系统管理）
   - 确认可以管理项目成员
   - 确认可以编辑所有项目内任务

4. **超级管理员验证**
   - 登录超级管理员账号
   - 确认可以访问所有功能
   - 确认可以分配用户角色

## 常见问题

### Q: 部署后菜单没有显示权限控制？
A: 检查以下几点：
1. 确认角色菜单权限配置正确
2. 确认用户已分配相应角色
3. 确认前端权限指令已正确注册
4. 清除浏览器缓存重新登录

### Q: 用户无法访问项目看板？
A: 检查：
1. 用户是否有项目成员或更高角色
2. 用户是否被添加到具体项目中
3. 项目权限配置是否正确

### Q: 权限验证不生效？
A: 检查：
1. 后端权限验证中间件是否正确配置
2. 前端路由守卫是否添加权限检查
3. 权限常量定义是否正确

## 回滚方案

如果需要回滚权限系统：

1. **数据库回滚**
```sql
-- 删除项目成员表
DROP TABLE IF EXISTS rd_project_members;

-- 删除新增的角色
DELETE FROM sys_role WHERE role_key IN ('project_manager', 'project_member');

-- 删除角色菜单关联
DELETE FROM sys_role_menu WHERE role_id IN (3, 4);
```

2. **代码回滚**
- 移除项目成员相关的控制器注册
- 移除前端权限指令注册
- 移除路由权限检查

## 注意事项

1. **数据备份**：部署前请备份数据库
2. **测试环境**：建议先在测试环境验证
3. **用户通知**：部署后通知用户权限变更
4. **监控日志**：关注权限相关的错误日志
5. **性能影响**：权限检查可能对性能有轻微影响

## 技术支持

如遇到问题，请检查：
1. 日志文件中的错误信息
2. 数据库连接和权限
3. 前后端版本兼容性
4. 浏览器控制台错误信息
