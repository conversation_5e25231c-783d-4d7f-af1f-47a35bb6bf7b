from sqlalchemy.ext.asyncio import AsyncSession
from typing import List
from exceptions.exception import ServiceException
from module_admin.entity.vo.common_vo import CrudResponseModel
from module_business.entity.vo.project_members_vo import (
    ProjectMembersModel,
    ProjectMembersPageQueryModel,
    DeleteProjectMembersModel
)
from module_business.dao.project_members_dao import ProjectMembersDao
from utils.common_util import CamelCaseUtil


class ProjectMembersService:
    """
    项目成员管理模块服务层
    """

    @classmethod
    async def get_project_members_list_services(
        cls, query_db: AsyncSession, query_object: ProjectMembersPageQueryModel, is_page: bool = False
    ):
        """
        获取项目成员列表信息service

        :param query_db: orm对象
        :param query_object: 查询参数对象
        :param is_page: 是否开启分页
        :return: 项目成员列表信息对象
        """
        project_members_list_result = await ProjectMembersDao.get_project_members_list(query_db, query_object, is_page)

        return project_members_list_result

    @classmethod
    async def add_project_member_services(cls, query_db: AsyncSession, page_object: ProjectMembersModel):
        """
        新增项目成员信息service

        :param query_db: orm对象
        :param page_object: 新增项目成员对象
        :return: 新增项目成员校验结果
        """
        # 检查项目成员是否已存在
        existing_member = await ProjectMembersDao.get_project_member_detail_by_id(
            query_db, page_object.project_id, page_object.user_id
        )
        if existing_member:
            raise ServiceException(message='该用户已是项目成员')

        try:
            await ProjectMembersDao.add_project_member_dao(query_db, page_object)
            await query_db.commit()
            return CrudResponseModel(is_success=True, message='新增成功')
        except Exception as e:
            await query_db.rollback()
            raise e

    @classmethod
    async def edit_project_member_services(cls, query_db: AsyncSession, page_object: ProjectMembersModel):
        """
        编辑项目成员信息service

        :param query_db: orm对象
        :param page_object: 编辑项目成员对象
        :return: 编辑项目成员校验结果
        """
        # 检查项目成员是否存在
        existing_member = await ProjectMembersDao.get_project_member_detail_by_id(
            query_db, page_object.project_id, page_object.user_id
        )
        if not existing_member:
            raise ServiceException(message='项目成员不存在')

        try:
            await ProjectMembersDao.edit_project_member_dao(query_db, page_object)
            await query_db.commit()
            return CrudResponseModel(is_success=True, message='更新成功')
        except Exception as e:
            await query_db.rollback()
            raise e

    @classmethod
    async def delete_project_member_services(cls, query_db: AsyncSession, delete_object: DeleteProjectMembersModel):
        """
        删除项目成员信息service

        :param query_db: orm对象
        :param delete_object: 删除项目成员对象
        :return: 删除项目成员校验结果
        """
        if delete_object.project_ids and delete_object.user_ids:
            project_id_list = delete_object.project_ids.split(',')
            user_id_list = delete_object.user_ids.split(',')
            
            if len(project_id_list) != len(user_id_list):
                raise ServiceException(message='项目ID和用户ID数量不匹配')
            
            try:
                for project_id, user_id in zip(project_id_list, user_id_list):
                    delete_member = ProjectMembersModel(
                        project_id=int(project_id),
                        user_id=int(user_id)
                    )
                    await ProjectMembersDao.delete_project_member_dao(query_db, delete_member)
                await query_db.commit()
                return CrudResponseModel(is_success=True, message='删除成功')
            except Exception as e:
                await query_db.rollback()
                raise e
        else:
            raise ServiceException(message='项目ID和用户ID不能为空')

    @classmethod
    async def project_member_detail_services(cls, query_db: AsyncSession, project_id: int, user_id: int):
        """
        获取项目成员详细信息service

        :param query_db: orm对象
        :param project_id: 项目ID
        :param user_id: 用户ID
        :return: 项目成员详细信息对象
        """
        project_member = await ProjectMembersDao.get_project_member_detail_by_id(query_db, project_id, user_id)
        if project_member:
            result = CamelCaseUtil.transform_result(project_member)
            return ProjectMembersModel(**result)
        else:
            return ProjectMembersModel()

    @classmethod
    async def check_user_project_permission_services(
        cls, query_db: AsyncSession, user_id: int, project_id: int, required_role: str = None
    ):
        """
        检查用户项目权限service

        :param query_db: orm对象
        :param user_id: 用户ID
        :param project_id: 项目ID
        :param required_role: 需要的角色
        :return: 权限检查结果
        """
        has_permission = await ProjectMembersDao.check_user_project_permission(
            query_db, user_id, project_id, required_role
        )
        return has_permission

    @classmethod
    async def get_user_projects_services(cls, query_db: AsyncSession, user_id: int):
        """
        获取用户参与的所有项目service

        :param query_db: orm对象
        :param user_id: 用户ID
        :return: 用户项目列表
        """
        user_projects = await ProjectMembersDao.get_user_projects(query_db, user_id)
        result = []
        for project in user_projects:
            project_data = CamelCaseUtil.transform_result(project)
            result.append(ProjectMembersModel(**project_data))
        return result

    @classmethod
    async def get_project_managers_services(cls, query_db: AsyncSession, project_id: int):
        """
        获取项目管理员列表service

        :param query_db: orm对象
        :param project_id: 项目ID
        :return: 项目管理员列表
        """
        project_managers = await ProjectMembersDao.get_project_managers(query_db, project_id)
        result = []
        for manager in project_managers:
            manager_data = CamelCaseUtil.transform_result(manager)
            result.append(ProjectMembersModel(**manager_data))
        return result

    @classmethod
    async def batch_add_project_members_services(
        cls, query_db: AsyncSession, project_id: int, user_ids: List[int], role: str, assigned_by: int
    ):
        """
        批量添加项目成员service

        :param query_db: orm对象
        :param project_id: 项目ID
        :param user_ids: 用户ID列表
        :param role: 项目角色
        :param assigned_by: 分配者ID
        :return: 批量添加结果
        """
        try:
            from datetime import datetime
            for user_id in user_ids:
                # 检查是否已存在
                existing_member = await ProjectMembersDao.get_project_member_detail_by_id(
                    query_db, project_id, user_id
                )
                if not existing_member:
                    member = ProjectMembersModel(
                        project_id=project_id,
                        user_id=user_id,
                        role=role,
                        assigned_at=datetime.now(),
                        assigned_by=assigned_by
                    )
                    await ProjectMembersDao.add_project_member_dao(query_db, member)
            
            await query_db.commit()
            return CrudResponseModel(is_success=True, message='批量添加成功')
        except Exception as e:
            await query_db.rollback()
            raise e
