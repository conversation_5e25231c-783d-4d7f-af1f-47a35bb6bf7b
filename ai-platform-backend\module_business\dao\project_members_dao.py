from datetime import datetime, time
from sqlalchemy import and_, delete, desc, func, or_, select, update
from sqlalchemy.ext.asyncio import AsyncSession
from module_business.entity.do.project_members_do import RdProjectMembers
from module_business.entity.vo.project_members_vo import (
    ProjectMembersModel,
    ProjectMembersPageQueryModel,
    ProjectMemberWithUserInfoModel
)
from module_admin.entity.do.user_do import SysUser
from module_admin.entity.do.dept_do import SysDept
from utils.page_util import PageUtil


class ProjectMembersDao:
    """
    项目成员管理模块数据库操作层
    """

    @classmethod
    async def get_project_members_list(
        cls, db: AsyncSession, query_object: ProjectMembersPageQueryModel, is_page: bool = False
    ):
        """
        根据查询参数获取项目成员列表信息

        :param db: orm对象
        :param query_object: 查询参数对象
        :param is_page: 是否开启分页
        :return: 项目成员列表信息对象
        """
        query = (
            select(
                RdProjectMembers.project_id,
                RdProjectMembers.user_id,
                RdProjectMembers.role,
                RdProjectMembers.assigned_at,
                RdProjectMembers.assigned_by,
                SysUser.user_name,
                SysUser.nick_name,
                SysUser.email,
                SysUser.phonenumber,
                SysDept.dept_name
            )
            .select_from(RdProjectMembers)
            .join(SysUser, RdProjectMembers.user_id == SysUser.user_id)
            .join(SysDept, SysUser.dept_id == SysDept.dept_id, isouter=True)
            .where(
                RdProjectMembers.project_id == query_object.project_id
                if query_object.project_id
                else True,
                RdProjectMembers.user_id == query_object.user_id
                if query_object.user_id
                else True,
                RdProjectMembers.role == query_object.role
                if query_object.role
                else True,
                RdProjectMembers.assigned_at.between(
                    datetime.combine(datetime.strptime(query_object.begin_time, '%Y-%m-%d'), time(0, 0, 0)),
                    datetime.combine(datetime.strptime(query_object.end_time, '%Y-%m-%d'), time(23, 59, 59)),
                )
                if query_object.begin_time and query_object.end_time
                else True,
            )
            .order_by(desc(RdProjectMembers.assigned_at))
            .distinct()
        )
        project_members_list = await PageUtil.paginate(db, query, query_object.page_num, query_object.page_size, is_page)

        return project_members_list

    @classmethod
    async def add_project_member_dao(cls, db: AsyncSession, project_member: ProjectMembersModel):
        """
        新增项目成员数据库操作

        :param db: orm对象
        :param project_member: 项目成员对象
        :return: 新增校验结果
        """
        db_project_member = RdProjectMembers(**project_member.model_dump())
        db.add(db_project_member)
        await db.flush()

        return db_project_member

    @classmethod
    async def edit_project_member_dao(cls, db: AsyncSession, project_member: ProjectMembersModel):
        """
        编辑项目成员数据库操作

        :param db: orm对象
        :param project_member: 项目成员对象
        :return: 编辑校验结果
        """
        await db.execute(
            update(RdProjectMembers)
            .where(
                and_(
                    RdProjectMembers.project_id == project_member.project_id,
                    RdProjectMembers.user_id == project_member.user_id
                )
            )
            .values(**project_member.model_dump(exclude_unset=True))
        )

    @classmethod
    async def delete_project_member_dao(cls, db: AsyncSession, project_member: ProjectMembersModel):
        """
        删除项目成员数据库操作

        :param db: orm对象
        :param project_member: 项目成员对象
        :return: 删除校验结果
        """
        await db.execute(
            delete(RdProjectMembers).where(
                and_(
                    RdProjectMembers.project_id == project_member.project_id,
                    RdProjectMembers.user_id == project_member.user_id
                )
            )
        )

    @classmethod
    async def get_project_member_detail_by_id(cls, db: AsyncSession, project_id: int, user_id: int):
        """
        根据项目ID和用户ID获取项目成员详细信息

        :param db: orm对象
        :param project_id: 项目ID
        :param user_id: 用户ID
        :return: 项目成员详细信息对象
        """
        project_member_info = (
            await db.execute(
                select(RdProjectMembers).where(
                    and_(
                        RdProjectMembers.project_id == project_id,
                        RdProjectMembers.user_id == user_id
                    )
                )
            )
        ).scalars().first()

        return project_member_info

    @classmethod
    async def get_user_projects(cls, db: AsyncSession, user_id: int):
        """
        获取用户参与的所有项目

        :param db: orm对象
        :param user_id: 用户ID
        :return: 用户项目列表
        """
        user_projects = (
            await db.execute(
                select(RdProjectMembers).where(RdProjectMembers.user_id == user_id)
            )
        ).scalars().all()

        return user_projects

    @classmethod
    async def get_project_managers(cls, db: AsyncSession, project_id: int):
        """
        获取项目的所有管理员

        :param db: orm对象
        :param project_id: 项目ID
        :return: 项目管理员列表
        """
        project_managers = (
            await db.execute(
                select(RdProjectMembers).where(
                    and_(
                        RdProjectMembers.project_id == project_id,
                        RdProjectMembers.role == 'manager'
                    )
                )
            )
        ).scalars().all()

        return project_managers

    @classmethod
    async def check_user_project_permission(cls, db: AsyncSession, user_id: int, project_id: int, required_role: str = None):
        """
        检查用户是否有项目权限

        :param db: orm对象
        :param user_id: 用户ID
        :param project_id: 项目ID
        :param required_role: 需要的角色（可选）
        :return: 权限检查结果
        """
        query = select(RdProjectMembers).where(
            and_(
                RdProjectMembers.user_id == user_id,
                RdProjectMembers.project_id == project_id
            )
        )
        
        if required_role:
            query = query.where(RdProjectMembers.role == required_role)
        
        result = (await db.execute(query)).scalars().first()
        return result is not None

    @classmethod
    async def delete_project_members_by_project_id(cls, db: AsyncSession, project_id: int):
        """
        根据项目ID删除所有项目成员

        :param db: orm对象
        :param project_id: 项目ID
        :return: 删除校验结果
        """
        await db.execute(
            delete(RdProjectMembers).where(RdProjectMembers.project_id == project_id)
        )
