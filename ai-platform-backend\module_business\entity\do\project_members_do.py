from sqlalchemy import BigInteger, TIMESTAMP, Column, String
from config.database import Base


class RdProjectMembers(Base):
    """
    项目成员关联表
    """

    __tablename__ = 'rd_project_members'

    project_id = Column(BigInteger, primary_key=True, nullable=False, comment='项目ID')
    user_id = Column(BigInteger, primary_key=True, nullable=False, comment='用户ID')
    role = Column(String(20), nullable=False, default='member', comment='项目角色：manager(项目管理员), member(项目成员)')
    assigned_at = Column(TIMESTAMP, nullable=True, comment='分配时间')
    assigned_by = Column(BigInteger, nullable=False, comment='分配者ID')

