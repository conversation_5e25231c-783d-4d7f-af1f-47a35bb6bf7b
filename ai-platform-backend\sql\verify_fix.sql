-- 验证修复结果的SQL脚本
-- 确认菜单结构正确，无重复，权限配置正确
-- 执行时间: 2024年

-- ==========================================
-- 1. 验证主菜单结构
-- ==========================================
SELECT '=== 业务主菜单验证 ===' as section;
SELECT 
    menu_id,
    menu_name,
    path,
    perms,
    icon,
    CASE 
        WHEN menu_name = '工具广场' AND path = 'tools' THEN '✓'
        WHEN menu_name = '项目看板' AND path = 'projects' THEN '✓'
        WHEN menu_name = '数据库管理' AND path = 'knowledge-bases' THEN '✓'
        ELSE '✗'
    END as status
FROM sys_menu 
WHERE menu_id IN (2000, 2001, 2002) AND parent_id = 0
ORDER BY menu_id;

-- ==========================================
-- 2. 验证无重复菜单
-- ==========================================
SELECT '=== 重复菜单检查 ===' as section;
SELECT 
    CASE 
        WHEN COUNT(*) = 0 THEN '✓ 无重复菜单'
        ELSE CONCAT('✗ 发现 ', COUNT(*), ' 组重复菜单')
    END as duplicate_check
FROM (
    SELECT menu_name, COUNT(*) as cnt
    FROM sys_menu 
    WHERE menu_id >= 2000 AND status = '0' AND menu_type = 'M'
    GROUP BY menu_name 
    HAVING COUNT(*) > 1
) as duplicates;

-- ==========================================
-- 3. 验证无子菜单（扁平结构）
-- ==========================================
SELECT '=== 子菜单检查 ===' as section;
SELECT 
    CASE 
        WHEN COUNT(*) = 0 THEN '✓ 无子菜单，结构扁平'
        ELSE CONCAT('✗ 发现 ', COUNT(*), ' 个子菜单')
    END as child_menu_check
FROM sys_menu 
WHERE parent_id IN (2000, 2001, 2002) AND menu_type = 'C' AND status = '0';

-- ==========================================
-- 4. 验证角色权限配置
-- ==========================================
SELECT '=== 角色权限验证 ===' as section;

-- 普通用户权限验证
SELECT 
    '普通用户' as role_name,
    GROUP_CONCAT(m.menu_name ORDER BY m.menu_id) as accessible_menus,
    CASE 
        WHEN GROUP_CONCAT(m.menu_name ORDER BY m.menu_id) = '工具广场' THEN '✓'
        ELSE '✗'
    END as status
FROM sys_role r
JOIN sys_role_menu rm ON r.role_id = rm.role_id
JOIN sys_menu m ON rm.menu_id = m.menu_id
WHERE r.role_id = 2 AND m.menu_id >= 2000;

-- 项目成员权限验证
SELECT 
    '项目成员' as role_name,
    GROUP_CONCAT(m.menu_name ORDER BY m.menu_id) as accessible_menus,
    CASE 
        WHEN GROUP_CONCAT(m.menu_name ORDER BY m.menu_id) = '工具广场,项目看板' THEN '✓'
        ELSE '✗'
    END as status
FROM sys_role r
JOIN sys_role_menu rm ON r.role_id = rm.role_id
JOIN sys_menu m ON rm.menu_id = m.menu_id
WHERE r.role_id = 101 AND m.menu_id >= 2000;

-- 项目管理员权限验证
SELECT 
    '项目管理员' as role_name,
    GROUP_CONCAT(m.menu_name ORDER BY m.menu_id) as accessible_menus,
    CASE 
        WHEN GROUP_CONCAT(m.menu_name ORDER BY m.menu_id) = '工具广场,项目看板,数据库管理' THEN '✓'
        ELSE '✗'
    END as status
FROM sys_role r
JOIN sys_role_menu rm ON r.role_id = rm.role_id
JOIN sys_menu m ON rm.menu_id = m.menu_id
WHERE r.role_id = 100 AND m.menu_id >= 2000;

-- ==========================================
-- 5. 验证路径正确性
-- ==========================================
SELECT '=== 路径验证 ===' as section;
SELECT 
    menu_name,
    path,
    CASE 
        WHEN menu_name = '工具广场' AND path = 'tools' THEN '✓ 正确'
        WHEN menu_name = '项目看板' AND path = 'projects' THEN '✓ 正确'
        WHEN menu_name = '数据库管理' AND path = 'knowledge-bases' THEN '✓ 正确'
        ELSE '✗ 路径错误'
    END as path_status
FROM sys_menu 
WHERE menu_id IN (2000, 2001, 2002);

-- ==========================================
-- 6. 验证权限标识
-- ==========================================
SELECT '=== 权限标识验证 ===' as section;
SELECT 
    menu_name,
    perms,
    CASE 
        WHEN menu_name = '工具广场' AND perms = 'business:tools:list' THEN '✓ 正确'
        WHEN menu_name = '项目看板' AND perms = 'business:projects:list' THEN '✓ 正确'
        WHEN menu_name = '数据库管理' AND perms = 'business:knowledge-bases:list' THEN '✓ 正确'
        ELSE '✗ 权限标识错误'
    END as perms_status
FROM sys_menu 
WHERE menu_id IN (2000, 2001, 2002);

-- ==========================================
-- 7. 检查是否存在问题路径
-- ==========================================
SELECT '=== 问题路径检查 ===' as section;

-- 检查是否还有database路径
SELECT 
    CASE 
        WHEN COUNT(*) = 0 THEN '✓ 无database路径菜单'
        ELSE CONCAT('✗ 仍有 ', COUNT(*), ' 个database路径菜单')
    END as database_path_check
FROM sys_menu 
WHERE path = 'database' AND status = '0';

-- 检查是否还有list路径冲突
SELECT 
    CASE 
        WHEN COUNT(*) = 0 THEN '✓ 无list路径冲突'
        ELSE CONCAT('✗ 仍有 ', COUNT(*), ' 个list路径菜单')
    END as list_path_check
FROM sys_menu 
WHERE path = 'list' AND parent_id >= 2000 AND status = '0';

-- ==========================================
-- 8. 最终状态总结
-- ==========================================
SELECT '=== 修复状态总结 ===' as section;

SELECT 
    '主菜单数量' as item,
    COUNT(*) as count,
    CASE 
        WHEN COUNT(*) = 3 THEN '✓ 正确'
        ELSE '✗ 异常'
    END as status
FROM sys_menu 
WHERE menu_id IN (2000, 2001, 2002) AND status = '0'
UNION ALL
SELECT 
    '子菜单数量' as item,
    COUNT(*) as count,
    CASE 
        WHEN COUNT(*) = 0 THEN '✓ 正确（扁平结构）'
        ELSE '✗ 异常（存在子菜单）'
    END as status
FROM sys_menu 
WHERE parent_id IN (2000, 2001, 2002) AND menu_type = 'C' AND status = '0'
UNION ALL
SELECT 
    '功能按钮数量' as item,
    COUNT(*) as count,
    CASE 
        WHEN COUNT(*) > 0 THEN '✓ 已配置'
        ELSE '- 未配置'
    END as status
FROM sys_menu 
WHERE parent_id IN (2000, 2001, 2002) AND menu_type = 'F' AND status = '0'
UNION ALL
SELECT 
    '角色权限配置' as item,
    COUNT(DISTINCT rm.role_id) as count,
    CASE 
        WHEN COUNT(DISTINCT rm.role_id) = 3 THEN '✓ 正确'
        ELSE '✗ 异常'
    END as status
FROM sys_role_menu rm
JOIN sys_menu m ON rm.menu_id = m.menu_id
WHERE rm.role_id IN (2, 100, 101) AND m.menu_id >= 2000;
