-- 创建项目成员关联表
-- 执行时间: 2024年
-- 描述: 用于管理项目成员关系，支持项目管理员和项目成员两种角色

-- MySQL版本
DROP TABLE IF EXISTS rd_project_members;
CREATE TABLE rd_project_members (
    project_id BIGINT NOT NULL COMMENT '项目ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    role VARCHAR(20) NOT NULL DEFAULT 'member' COMMENT '项目角色：manager(项目管理员), member(项目成员)',
    assigned_at TIMESTAMP NULL COMMENT '分配时间',
    assigned_by BIGINT NOT NULL COMMENT '分配者ID',
    PRIMARY KEY (project_id, user_id),
    INDEX idx_project_members_project_id (project_id),
    INDEX idx_project_members_user_id (user_id),
    INDEX idx_project_members_role (role),
    FOREIG<PERSON> KEY (project_id) REFERENCES rd_projects(project_id) ON DELETE CASCADE,
    <PERSON>OR<PERSON><PERSON><PERSON> KEY (user_id) REFERENCES sys_user(user_id) ON DELETE CASCADE,
    FOREIG<PERSON> KEY (assigned_by) REFERENCES sys_user(user_id)
) ENGINE=InnoDB COMMENT='项目成员关联表';

-- PostgreSQL版本
-- DROP TABLE IF EXISTS rd_project_members;
-- CREATE TABLE rd_project_members (
--     project_id BIGINT NOT NULL,
--     user_id BIGINT NOT NULL,
--     role VARCHAR(20) NOT NULL DEFAULT 'member',
--     assigned_at TIMESTAMP NULL,
--     assigned_by BIGINT NOT NULL,
--     PRIMARY KEY (project_id, user_id)
-- );
-- 
-- COMMENT ON COLUMN rd_project_members.project_id IS '项目ID';
-- COMMENT ON COLUMN rd_project_members.user_id IS '用户ID';
-- COMMENT ON COLUMN rd_project_members.role IS '项目角色：manager(项目管理员), member(项目成员)';
-- COMMENT ON COLUMN rd_project_members.assigned_at IS '分配时间';
-- COMMENT ON COLUMN rd_project_members.assigned_by IS '分配者ID';
-- COMMENT ON TABLE rd_project_members IS '项目成员关联表';
-- 
-- CREATE INDEX idx_project_members_project_id ON rd_project_members(project_id);
-- CREATE INDEX idx_project_members_user_id ON rd_project_members(user_id);
-- CREATE INDEX idx_project_members_role ON rd_project_members(role);

-- 初始化数据示例（可选）
-- INSERT INTO rd_project_members (project_id, user_id, role, assigned_at, assigned_by) VALUES
-- (1, 1, 'manager', NOW(), 1),
-- (1, 2, 'member', NOW(), 1);
