-- 权限系统修复SQL脚本
-- 适配现有菜单结构：项目看板 + 数据库管理
-- 执行时间: 2024年

-- ==========================================
-- 1. 创建项目成员关联表
-- ==========================================
CREATE TABLE IF NOT EXISTS `rd_project_members` (
  `project_id` bigint NOT NULL COMMENT '项目ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `role` varchar(20) NOT NULL DEFAULT 'member' COMMENT '项目角色：manager(项目管理员), member(项目成员)',
  `assigned_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '分配时间',
  `assigned_by` bigint NOT NULL COMMENT '分配者ID',
  PRIMARY KEY (`project_id`, `user_id`),
  KEY `idx_project_members_project_id` (`project_id`),
  KEY `idx_project_members_user_id` (`user_id`),
  KEY `idx_project_members_role` (`role`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='项目成员关联表';

-- ==========================================
-- 2. 创建项目相关角色
-- ==========================================
-- 插入项目管理员角色
INSERT IGNORE INTO `sys_role` (`role_id`, `role_name`, `role_key`, `role_sort`, `data_scope`, `menu_check_strictly`, `dept_check_strictly`, `status`, `del_flag`, `create_by`, `create_time`, `remark`) 
VALUES (100, '项目管理员', 'project_manager', 3, '1', 1, 1, '0', '0', 'admin', NOW(), '项目管理员角色，负责项目全生命周期管理');

-- 插入项目成员角色
INSERT IGNORE INTO `sys_role` (`role_id`, `role_name`, `role_key`, `role_sort`, `data_scope`, `menu_check_strictly`, `dept_check_strictly`, `status`, `del_flag`, `create_by`, `create_time`, `remark`) 
VALUES (101, '项目成员', 'project_member', 4, '1', 1, 1, '0', '0', 'admin', NOW(), '项目成员角色，可查看项目任务并创建自己的任务');

-- ==========================================
-- 3. 为现有菜单添加权限标识（如果缺少）
-- ==========================================
-- 更新工具广场菜单权限
UPDATE `sys_menu` SET `perms` = 'business:tools:list' 
WHERE `path` = 'tools' AND (`perms` IS NULL OR `perms` = '');

-- 更新项目看板菜单权限
UPDATE `sys_menu` SET `perms` = 'business:projects:list' 
WHERE `path` = 'projects' AND (`perms` IS NULL OR `perms` = '');

-- 更新数据库管理菜单权限
UPDATE `sys_menu` SET `perms` = 'business:knowledge-bases:list' 
WHERE `path` = 'knowledge-bases' AND (`perms` IS NULL OR `perms` = '');

-- ==========================================
-- 4. 配置角色菜单权限
-- ==========================================
-- 获取菜单ID的变量设置
SET @tools_menu_id = (SELECT menu_id FROM sys_menu WHERE path = 'tools' LIMIT 1);
SET @projects_menu_id = (SELECT menu_id FROM sys_menu WHERE path = 'projects' LIMIT 1);
SET @kb_menu_id = (SELECT menu_id FROM sys_menu WHERE path = 'knowledge-bases' LIMIT 1);
SET @index_menu_id = (SELECT menu_id FROM sys_menu WHERE path = 'index' OR path = '' AND parent_id = 0 LIMIT 1);

-- 项目管理员权限 (role_id = 100)
-- 拥有：首页 + 工具广场 + 项目看板 + 数据库管理
DELETE FROM `sys_role_menu` WHERE `role_id` = 100;
INSERT IGNORE INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES
(100, @index_menu_id),
(100, @tools_menu_id),
(100, @projects_menu_id),
(100, @kb_menu_id);

-- 项目成员权限 (role_id = 101)  
-- 拥有：首页 + 工具广场 + 项目看板
DELETE FROM `sys_role_menu` WHERE `role_id` = 101;
INSERT IGNORE INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES
(101, @index_menu_id),
(101, @tools_menu_id),
(101, @projects_menu_id);

-- 普通用户权限 (假设role_id = 2)
-- 拥有：首页 + 工具广场
DELETE FROM `sys_role_menu` WHERE `role_id` = 2;
INSERT IGNORE INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES
(2, @index_menu_id),
(2, @tools_menu_id);

-- ==========================================
-- 5. 添加功能按钮权限（可选）
-- ==========================================
-- 为项目看板添加功能按钮权限
INSERT IGNORE INTO `sys_menu` (`menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `remark`) VALUES
('项目查询', @projects_menu_id, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'business:projects:query', '#', 'admin', NOW(), ''),
('项目新增', @projects_menu_id, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'business:projects:add', '#', 'admin', NOW(), ''),
('项目修改', @projects_menu_id, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'business:projects:edit', '#', 'admin', NOW(), ''),
('项目删除', @projects_menu_id, 4, '', '', '', '', 1, 0, 'F', '0', '0', 'business:projects:remove', '#', 'admin', NOW(), ''),
('任务查询', @projects_menu_id, 5, '', '', '', '', 1, 0, 'F', '0', '0', 'business:tasks:query', '#', 'admin', NOW(), ''),
('任务新增', @projects_menu_id, 6, '', '', '', '', 1, 0, 'F', '0', '0', 'business:tasks:add', '#', 'admin', NOW(), ''),
('任务修改', @projects_menu_id, 7, '', '', '', '', 1, 0, 'F', '0', '0', 'business:tasks:edit', '#', 'admin', NOW(), ''),
('任务删除', @projects_menu_id, 8, '', '', '', '', 1, 0, 'F', '0', '0', 'business:tasks:remove', '#', 'admin', NOW(), '');

-- 为数据库管理添加功能按钮权限
INSERT IGNORE INTO `sys_menu` (`menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `remark`) VALUES
('知识库查询', @kb_menu_id, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'business:knowledge-bases:query', '#', 'admin', NOW(), ''),
('知识库新增', @kb_menu_id, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'business:knowledge-bases:add', '#', 'admin', NOW(), ''),
('知识库修改', @kb_menu_id, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'business:knowledge-bases:edit', '#', 'admin', NOW(), ''),
('知识库删除', @kb_menu_id, 4, '', '', '', '', 1, 0, 'F', '0', '0', 'business:knowledge-bases:remove', '#', 'admin', NOW(), '');

-- 为工具广场添加功能按钮权限
INSERT IGNORE INTO `sys_menu` (`menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `remark`) VALUES
('工具查询', @tools_menu_id, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'business:tools:query', '#', 'admin', NOW(), ''),
('工具使用', @tools_menu_id, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'business:tools:use', '#', 'admin', NOW(), '');

-- ==========================================
-- 6. 示例数据（可选）
-- ==========================================
-- 为测试用户分配角色（请根据实际用户ID调整）
-- INSERT IGNORE INTO `sys_user_role` (`user_id`, `role_id`) VALUES (2, 100); -- 用户2设为项目管理员
-- INSERT IGNORE INTO `sys_user_role` (`user_id`, `role_id`) VALUES (3, 101); -- 用户3设为项目成员

-- 添加示例项目成员关系（请根据实际项目ID和用户ID调整）
-- INSERT IGNORE INTO `rd_project_members` (`project_id`, `user_id`, `role`, `assigned_at`, `assigned_by`) VALUES
-- (1, 2, 'manager', NOW(), 1),
-- (1, 3, 'member', NOW(), 1);

-- ==========================================
-- 7. 验证查询
-- ==========================================
-- 查看角色配置
-- SELECT * FROM sys_role WHERE role_key IN ('project_manager', 'project_member');

-- 查看菜单权限配置
-- SELECT r.role_name, m.menu_name, m.path, m.perms 
-- FROM sys_role r 
-- JOIN sys_role_menu rm ON r.role_id = rm.role_id 
-- JOIN sys_menu m ON rm.menu_id = m.menu_id 
-- WHERE r.role_id IN (100, 101, 2) 
-- ORDER BY r.role_id, m.order_num;

-- 查看项目成员表
-- SELECT * FROM rd_project_members;

COMMIT;
