import useUserStore from '@/store/modules/user'
import { checkPermi, checkRole } from '@/utils/permission'

/**
 * 项目权限相关的工具函数
 */

// 角色常量
export const ROLES = {
  ADMIN: 'admin',
  PROJECT_MANAGER: 'project_manager',
  PROJECT_MEMBER: 'project_member',
  COMMON: 'common'
}

// 项目内角色常量
export const PROJECT_ROLES = {
  MANAGER: 'manager',
  MEMBER: 'member'
}

// 权限常量
export const PERMISSIONS = {
  ALL: '*:*:*',
  
  // 项目管理权限
  PROJECT_LIST: 'business:projects:list',
  PROJECT_QUERY: 'business:projects:query',
  PROJECT_ADD: 'business:projects:add',
  PROJECT_EDIT: 'business:projects:edit',
  PROJECT_REMOVE: 'business:projects:remove',
  PROJECT_EXPORT: 'business:projects:export',
  
  // 项目成员管理权限
  PROJECT_MEMBER_LIST: 'business:project-members:list',
  PROJECT_MEMBER_QUERY: 'business:project-members:query',
  PROJECT_MEMBER_ADD: 'business:project-members:add',
  PROJECT_MEMBER_EDIT: 'business:project-members:edit',
  PROJECT_MEMBER_REMOVE: 'business:project-members:remove',
  
  // 任务管理权限
  TASK_LIST: 'business:tasks:list',
  TASK_QUERY: 'business:tasks:query',
  TASK_ADD: 'business:tasks:add',
  TASK_EDIT: 'business:tasks:edit',
  TASK_REMOVE: 'business:tasks:remove',
  TASK_EXPORT: 'business:tasks:export',
  
  // 知识库管理权限
  KB_LIST: 'business:knowledge-bases:list',
  KB_QUERY: 'business:knowledge-bases:query',
  KB_ADD: 'business:knowledge-bases:add',
  KB_EDIT: 'business:knowledge-bases:edit',
  KB_REMOVE: 'business:knowledge-bases:remove',
  
  // 工具管理权限
  TOOL_LIST: 'business:tools:list',
  TOOL_QUERY: 'business:tools:query',
  TOOL_USE: 'business:tools:use'
}

/**
 * 检查用户是否是超级管理员
 * @returns {Boolean}
 */
export function isAdmin() {
  return checkRole([ROLES.ADMIN])
}

/**
 * 检查用户是否是项目管理员角色
 * @returns {Boolean}
 */
export function isProjectManager() {
  return checkRole([ROLES.PROJECT_MANAGER]) || isAdmin()
}

/**
 * 检查用户是否是项目成员角色
 * @returns {Boolean}
 */
export function isProjectMember() {
  return checkRole([ROLES.PROJECT_MEMBER]) || isProjectManager()
}

/**
 * 检查用户是否只是普通用户
 * @returns {Boolean}
 */
export function isCommonUser() {
  const userStore = useUserStore()
  const roles = userStore.roles
  return roles.length === 1 && roles.includes(ROLES.COMMON)
}

/**
 * 检查用户是否有项目管理权限
 * @returns {Boolean}
 */
export function hasProjectManagePermission() {
  return checkPermi([
    PERMISSIONS.PROJECT_LIST,
    PERMISSIONS.PROJECT_QUERY,
    PERMISSIONS.PROJECT_ADD,
    PERMISSIONS.PROJECT_EDIT
  ])
}

/**
 * 检查用户是否有任务管理权限
 * @returns {Boolean}
 */
export function hasTaskManagePermission() {
  return checkPermi([
    PERMISSIONS.TASK_LIST,
    PERMISSIONS.TASK_QUERY,
    PERMISSIONS.TASK_ADD
  ])
}

/**
 * 检查用户是否有数据库管理权限
 * @returns {Boolean}
 */
export function hasDatabaseManagePermission() {
  return checkPermi([
    PERMISSIONS.KB_LIST,
    PERMISSIONS.KB_QUERY
  ]) || isProjectManager()
}

/**
 * 检查用户是否有工具使用权限
 * @returns {Boolean}
 */
export function hasToolUsePermission() {
  return checkPermi([
    PERMISSIONS.TOOL_LIST,
    PERMISSIONS.TOOL_QUERY,
    PERMISSIONS.TOOL_USE
  ])
}

/**
 * 检查用户对任务的操作权限
 * @param {Object} task 任务对象
 * @param {String} action 操作类型 ('view', 'edit', 'delete')
 * @returns {Boolean}
 */
export function checkTaskPermission(task, action = 'view') {
  if (isAdmin()) {
    return true
  }
  
  const userStore = useUserStore()
  const userId = userStore.id
  
  // 查看权限：项目成员即可（在组件中需要额外检查项目权限）
  if (action === 'view') {
    return hasTaskManagePermission()
  }
  
  // 编辑/删除权限：任务创建者或项目管理员
  if (action === 'edit' || action === 'delete') {
    // 检查是否是任务创建者
    if (task.assignedTo === userId) {
      return true
    }
    
    // 检查是否是项目管理员（需要在组件中额外验证项目权限）
    return isProjectManager()
  }
  
  return false
}

/**
 * 检查用户对项目的操作权限
 * @param {Object} project 项目对象
 * @param {String} action 操作类型 ('view', 'edit', 'delete', 'manage_members')
 * @returns {Boolean}
 */
export function checkProjectPermission(project, action = 'view') {
  if (isAdmin()) {
    return true
  }
  
  const userStore = useUserStore()
  const userId = userStore.id
  
  // 查看权限：项目成员即可
  if (action === 'view') {
    return hasProjectManagePermission()
  }
  
  // 编辑/删除/成员管理权限：项目所有者或项目管理员
  if (action === 'edit' || action === 'delete' || action === 'manage_members') {
    // 检查是否是项目所有者
    if (project.ownerId === userId) {
      return true
    }
    
    // 检查是否是项目管理员（需要在组件中额外验证项目权限）
    return isProjectManager()
  }
  
  return false
}

/**
 * 根据用户角色获取可访问的菜单（适配现有菜单结构）
 * @returns {Array} 菜单配置数组
 */
export function getAccessibleMenus() {
  const menus = []

  // 首页 - 所有用户都可以访问
  menus.push({
    name: 'index',
    path: '/index',
    title: '首页',
    icon: 'dashboard'
  })

  // 工具广场 - 所有用户都可以访问
  if (hasToolUsePermission()) {
    menus.push({
      name: 'tools',
      path: '/tools',
      title: '工具广场',
      icon: 'tools'
    })
  }

  // 项目看板 - 项目成员及以上角色可以访问
  if (hasProjectManagePermission()) {
    menus.push({
      name: 'projects',
      path: '/projects',
      title: '项目看板',
      icon: 'kanban'
    })
  }

  // 数据库管理 - 项目管理员及以上角色可以访问
  if (hasDatabaseManagePermission()) {
    menus.push({
      name: 'knowledge-bases',
      path: '/knowledge-bases',
      title: '数据库管理',
      icon: 'database'
    })
  }

  return menus
}

/**
 * 检查菜单是否应该显示（仅适配现有菜单：项目看板、数据库管理）
 * @param {String} menuName 菜单名称或路径
 * @returns {Boolean}
 */
export function shouldShowMenu(menuName) {
  // 超级管理员可以访问所有菜单
  if (isAdmin()) {
    return true
  }

  switch (menuName) {
    case 'index':
    case 'home':
    case '/':
    case '/index':
      return true // 首页所有用户都可以访问

    case 'tools':
    case '/tools':
      return hasToolUsePermission() // 工具广场所有用户都可以访问

    case 'projects':
    case '/projects':
    case 'project-board':
      return hasProjectManagePermission() // 项目看板需要项目权限

    case 'knowledge-bases':
    case '/knowledge-bases':
    case 'database':
      return hasDatabaseManagePermission() // 数据库管理需要管理员权限

    default:
      return false
  }
}

/**
 * 检查按钮是否应该显示
 * @param {String|Array} permissions 权限标识
 * @returns {Boolean}
 */
export function shouldShowButton(permissions) {
  if (typeof permissions === 'string') {
    permissions = [permissions]
  }
  return checkPermi(permissions)
}
