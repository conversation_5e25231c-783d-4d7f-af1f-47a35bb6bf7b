import { 
  isAdmin, 
  isProjectManager, 
  isProjectMember, 
  hasToolUsePermission,
  hasProjectManagePermission,
  hasDatabaseManagePermission
} from '@/utils/projectPermission'

/**
 * 路由权限验证工具（适配现有菜单结构）
 */

/**
 * 检查路由权限
 * @param {Object} route 路由对象
 * @returns {Boolean} 是否有权限访问
 */
export function checkRoutePermission(route) {
  const path = route.path
  
  // 公共路由，所有用户都可以访问
  const publicRoutes = [
    '/',
    '/index',
    '/login',
    '/register',
    '/401',
    '/404',
    '/user/profile',
    '/home/<USER>'
  ]
  
  if (publicRoutes.includes(path) || path.startsWith('/redirect')) {
    return true
  }
  
  // 超级管理员可以访问所有路由
  if (isAdmin()) {
    return true
  }
  
  // 根据路径检查权限
  if (path.startsWith('/tools')) {
    return hasToolUsePermission()
  }

  if (path.startsWith('/projects')) {
    return hasProjectManagePermission()
  }

  if (path.startsWith('/knowledge-bases')) {
    return hasDatabaseManagePermission()
  }

  if (path.startsWith('/tasks')) {
    // 任务详情页面需要项目权限
    return hasProjectManagePermission()
  }

  // 处理具体的菜单路径
  if (path === '/projects/board') {
    return hasProjectManagePermission()
  }

  if (path === '/knowledge-bases/index') {
    return hasDatabaseManagePermission()
  }
  
  if (path.startsWith('/system')) {
    return isAdmin()
  }
  
  // 默认拒绝访问
  return false
}

/**
 * 获取用户可访问的路由列表
 * @returns {Array} 可访问的路由路径列表
 */
export function getAccessibleRoutes() {
  const routes = [
    '/',
    '/index',
    '/user/profile',
    '/home/<USER>'
  ]
  
  // 工具广场
  if (hasToolUsePermission()) {
    routes.push('/tools')
  }
  
  // 项目看板
  if (hasProjectManagePermission()) {
    routes.push('/projects')
    routes.push('/projects/board')
    routes.push('/projects/detail/:id')
    routes.push('/projects/create-task/:projectId')
    routes.push('/tasks/detail/:id')
  }
  
  // 数据库管理
  if (hasDatabaseManagePermission()) {
    routes.push('/knowledge-bases')
    routes.push('/knowledge-bases/index')
    routes.push('/knowledge-bases/detail/:id')
  }
  
  // 系统管理
  if (isAdmin()) {
    routes.push('/system')
    routes.push('/system/user')
    routes.push('/system/role')
    routes.push('/system/menu')
    routes.push('/system/dept')
    routes.push('/system/post')
    routes.push('/system/dict')
    routes.push('/system/config')
    routes.push('/system/notice')
    routes.push('/monitor')
    routes.push('/tool')
  }
  
  return routes
}

/**
 * 检查用户是否有权限访问特定功能
 * @param {String} feature 功能名称
 * @returns {Boolean} 是否有权限
 */
export function hasFeaturePermission(feature) {
  switch (feature) {
    case 'home':
    case 'profile':
    case 'chat':
      return true
    
    case 'tools':
      return hasToolUsePermission()
    
    case 'projects':
    case 'project-board':
    case 'project-detail':
    case 'create-task':
    case 'task-detail':
      return hasProjectManagePermission()
    
    case 'knowledge-bases':
    case 'database':
      return hasDatabaseManagePermission()
    
    case 'system':
    case 'user-management':
    case 'role-management':
    case 'menu-management':
      return isAdmin()
    
    default:
      return false
  }
}

/**
 * 根据用户权限过滤菜单项
 * @param {Array} menuItems 菜单项数组
 * @returns {Array} 过滤后的菜单项
 */
export function filterMenuItems(menuItems) {
  return menuItems.filter(item => {
    // 检查菜单项权限
    if (item.meta && item.meta.requiresAuth === false) {
      return true
    }
    
    if (item.path) {
      return checkRoutePermission({ path: item.path })
    }
    
    return true
  }).map(item => {
    // 递归过滤子菜单
    if (item.children && item.children.length > 0) {
      item.children = filterMenuItems(item.children)
    }
    return item
  })
}

/**
 * 获取重定向路径（当用户访问无权限页面时）
 * @returns {String} 重定向路径
 */
export function getRedirectPath() {
  if (hasProjectManagePermission()) {
    return '/projects'
  }
  
  if (hasToolUsePermission()) {
    return '/tools'
  }
  
  return '/index'
}

/**
 * 检查是否需要重定向
 * @param {String} currentPath 当前路径
 * @returns {String|null} 重定向路径或null
 */
export function checkRedirect(currentPath) {
  if (!checkRoutePermission({ path: currentPath })) {
    return getRedirectPath()
  }
  
  return null
}
