-- 权限管理功能重构 - 角色和权限配置
-- 执行时间: 2024年
-- 描述: 新增项目管理员和项目成员角色，配置相应的菜单权限和接口权限

-- 1. 新增角色
INSERT INTO sys_role (role_name, role_key, role_sort, data_scope, menu_check_strictly, dept_check_strictly, status, del_flag, create_by, create_time, remark) VALUES
('项目管理员', 'project_manager', 3, '1', 1, 1, '0', '0', 'admin', NOW(), '项目管理员角色，负责项目全生命周期管理'),
('项目成员', 'project_member', 4, '1', 1, 1, '0', '0', 'admin', NOW(), '项目成员角色，可查看项目任务并创建自己的任务');

-- 2. 新增业务相关菜单
-- 项目管理菜单
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark) VALUES
('项目管理', '0', '2', 'projects', NULL, '', '', 1, 0, 'M', '0', '0', '', 'project', 'admin', NOW(), '项目管理目录'),
('项目列表', '2000', '1', 'list', 'business/projects/index', '', '', 1, 0, 'C', '0', '0', 'business:projects:list', 'list', 'admin', NOW(), '项目列表菜单'),
('项目成员管理', '2000', '2', 'members', 'business/project-members/index', '', '', 1, 0, 'C', '0', '0', 'business:project-members:list', 'peoples', 'admin', NOW(), '项目成员管理菜单'),
('任务管理', '2000', '3', 'tasks', 'business/tasks/index', '', '', 1, 0, 'C', '0', '0', 'business:tasks:list', 'task', 'admin', NOW(), '任务管理菜单');

-- 数据库管理菜单
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark) VALUES
('数据库管理', '0', '3', 'database', NULL, '', '', 1, 0, 'M', '0', '0', '', 'database', 'admin', NOW(), '数据库管理目录'),
('知识库管理', '2004', '1', 'knowledge-bases', 'business/knowledge-bases/index', '', '', 1, 0, 'C', '0', '0', 'business:knowledge-bases:list', 'documentation', 'admin', NOW(), '知识库管理菜单');

-- 工具广场菜单
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark) VALUES
('工具广场', '0', '1', 'tools', NULL, '', '', 1, 0, 'M', '0', '0', '', 'tool', 'admin', NOW(), '工具广场目录'),
('工具列表', '2006', '1', 'list', 'business/tools/index', '', '', 1, 0, 'C', '0', '0', 'business:tools:list', 'component', 'admin', NOW(), '工具列表菜单');

-- 3. 新增项目相关按钮权限
-- 项目管理按钮
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark) VALUES
('项目查询', '2001', '1', '', '', '', '', 1, 0, 'F', '0', '0', 'business:projects:query', '#', 'admin', NOW(), ''),
('项目新增', '2001', '2', '', '', '', '', 1, 0, 'F', '0', '0', 'business:projects:add', '#', 'admin', NOW(), ''),
('项目修改', '2001', '3', '', '', '', '', 1, 0, 'F', '0', '0', 'business:projects:edit', '#', 'admin', NOW(), ''),
('项目删除', '2001', '4', '', '', '', '', 1, 0, 'F', '0', '0', 'business:projects:remove', '#', 'admin', NOW(), ''),
('项目导出', '2001', '5', '', '', '', '', 1, 0, 'F', '0', '0', 'business:projects:export', '#', 'admin', NOW(), '');

-- 项目成员管理按钮
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark) VALUES
('成员查询', '2002', '1', '', '', '', '', 1, 0, 'F', '0', '0', 'business:project-members:query', '#', 'admin', NOW(), ''),
('成员新增', '2002', '2', '', '', '', '', 1, 0, 'F', '0', '0', 'business:project-members:add', '#', 'admin', NOW(), ''),
('成员修改', '2002', '3', '', '', '', '', 1, 0, 'F', '0', '0', 'business:project-members:edit', '#', 'admin', NOW(), ''),
('成员删除', '2002', '4', '', '', '', '', 1, 0, 'F', '0', '0', 'business:project-members:remove', '#', 'admin', NOW(), '');

-- 任务管理按钮
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark) VALUES
('任务查询', '2003', '1', '', '', '', '', 1, 0, 'F', '0', '0', 'business:tasks:query', '#', 'admin', NOW(), ''),
('任务新增', '2003', '2', '', '', '', '', 1, 0, 'F', '0', '0', 'business:tasks:add', '#', 'admin', NOW(), ''),
('任务修改', '2003', '3', '', '', '', '', 1, 0, 'F', '0', '0', 'business:tasks:edit', '#', 'admin', NOW(), ''),
('任务删除', '2003', '4', '', '', '', '', 1, 0, 'F', '0', '0', 'business:tasks:remove', '#', 'admin', NOW(), ''),
('任务导出', '2003', '5', '', '', '', '', 1, 0, 'F', '0', '0', 'business:tasks:export', '#', 'admin', NOW(), '');

-- 知识库管理按钮
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark) VALUES
('知识库查询', '2005', '1', '', '', '', '', 1, 0, 'F', '0', '0', 'business:knowledge-bases:query', '#', 'admin', NOW(), ''),
('知识库新增', '2005', '2', '', '', '', '', 1, 0, 'F', '0', '0', 'business:knowledge-bases:add', '#', 'admin', NOW(), ''),
('知识库修改', '2005', '3', '', '', '', '', 1, 0, 'F', '0', '0', 'business:knowledge-bases:edit', '#', 'admin', NOW(), ''),
('知识库删除', '2005', '4', '', '', '', '', 1, 0, 'F', '0', '0', 'business:knowledge-bases:remove', '#', 'admin', NOW(), '');

-- 工具管理按钮
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark) VALUES
('工具查询', '2007', '1', '', '', '', '', 1, 0, 'F', '0', '0', 'business:tools:query', '#', 'admin', NOW(), ''),
('工具使用', '2007', '2', '', '', '', '', 1, 0, 'F', '0', '0', 'business:tools:use', '#', 'admin', NOW(), '');

-- 4. 配置角色菜单权限
-- 项目管理员权限 (role_id = 3)
-- 拥有项目管理、任务管理、数据库管理权限
INSERT INTO sys_role_menu (role_id, menu_id) VALUES
-- 工具广场
(3, 2006), (3, 2007), (3, 2025), (3, 2026),
-- 项目管理
(3, 2000), (3, 2001), (3, 2002), (3, 2003),
-- 项目管理按钮
(3, 2008), (3, 2009), (3, 2010), (3, 2011), (3, 2012),
-- 项目成员管理按钮
(3, 2013), (3, 2014), (3, 2015), (3, 2016),
-- 任务管理按钮
(3, 2017), (3, 2018), (3, 2019), (3, 2020), (3, 2021),
-- 数据库管理
(3, 2004), (3, 2005),
-- 知识库管理按钮
(3, 2022), (3, 2023), (3, 2024), (3, 2025);

-- 项目成员权限 (role_id = 4)
-- 拥有工具广场、项目管理（查看）、任务管理（有限）权限
INSERT INTO sys_role_menu (role_id, menu_id) VALUES
-- 工具广场
(4, 2006), (4, 2007), (4, 2025), (4, 2026),
-- 项目管理（仅查看）
(4, 2000), (4, 2001), (4, 2003),
-- 项目查询权限
(4, 2008),
-- 任务管理权限（查看和新增）
(4, 2017), (4, 2018);

-- 5. 更新普通用户角色权限 (role_id = 2)
-- 普通用户只能访问工具广场
DELETE FROM sys_role_menu WHERE role_id = 2;
INSERT INTO sys_role_menu (role_id, menu_id) VALUES
-- 工具广场
(2, 2006), (2, 2007), (2, 2025), (2, 2026);

-- 注意：菜单ID需要根据实际插入后的ID进行调整
-- 建议分步执行，先插入菜单，查看生成的ID，再配置角色菜单关联
