/**
 * 路由修复工具
 * 解决菜单路由不一致问题
 */

/**
 * 修复动态路由配置
 * @param {Array} routes 路由配置数组
 * @returns {Array} 修复后的路由配置
 */
export function fixDynamicRoutes(routes) {
  return routes.map(route => {
    // 修复项目看板路由
    if (route.path === 'projects' && route.name === 'Projects') {
      return {
        ...route,
        path: '/projects',
        redirect: '/projects/board',
        meta: { 
          title: '项目看板', 
          icon: 'kanban',
          requiresAuth: true 
        },
        children: [
          {
            path: 'board',
            name: 'ProjectsBoard',
            component: 'projects/board',
            meta: { 
              title: '项目看板', 
              icon: 'kanban',
              requiresAuth: true 
            }
          },
          {
            path: 'detail/:id',
            name: 'ProjectDetail',
            component: 'projects/detail',
            meta: { 
              title: '项目详情', 
              icon: 'kanban',
              requiresAuth: true 
            },
            hidden: true
          },
          {
            path: 'create-task/:projectId',
            name: 'CreateTask',
            component: 'projects/create-task',
            meta: { 
              title: '创建任务', 
              icon: 'kanban',
              requiresAuth: true 
            },
            hidden: true
          }
        ]
      }
    }
    
    // 修复数据库管理路由
    if (route.path === 'knowledge-bases' || route.path === 'database') {
      return {
        ...route,
        path: '/knowledge-bases',
        redirect: '/knowledge-bases/index',
        name: 'KnowledgeBases',
        meta: { 
          title: '数据库管理', 
          icon: 'database',
          requiresAuth: true 
        },
        children: [
          {
            path: 'index',
            name: 'KnowledgeBasesIndex',
            component: 'knowledge-bases/index',
            meta: { 
              title: '数据库管理', 
              icon: 'database',
              requiresAuth: true 
            }
          },
          {
            path: 'detail/:id',
            name: 'KnowledgeBaseDetail',
            component: 'knowledge-bases/detail',
            meta: { 
              title: '数据库详情', 
              icon: 'database',
              requiresAuth: true 
            },
            hidden: true
          }
        ]
      }
    }
    
    // 修复工具广场路由
    if (route.path === 'tools') {
      return {
        ...route,
        path: '/tools',
        name: 'Tools',
        component: 'tools/index',
        meta: { 
          title: '工具广场', 
          icon: 'tools',
          requiresAuth: false 
        }
      }
    }
    
    return route
  })
}

/**
 * 过滤重复路由
 * @param {Array} routes 路由配置数组
 * @returns {Array} 去重后的路由配置
 */
export function removeDuplicateRoutes(routes) {
  const seen = new Set()
  return routes.filter(route => {
    const key = `${route.path}-${route.name}`
    if (seen.has(key)) {
      return false
    }
    seen.add(key)
    return true
  })
}

/**
 * 验证路由配置
 * @param {Array} routes 路由配置数组
 * @returns {Object} 验证结果
 */
export function validateRoutes(routes) {
  const issues = []
  const paths = new Set()
  
  routes.forEach(route => {
    // 检查重复路径
    if (paths.has(route.path)) {
      issues.push(`重复路径: ${route.path}`)
    }
    paths.add(route.path)
    
    // 检查必要字段
    if (!route.name) {
      issues.push(`路由缺少name字段: ${route.path}`)
    }
    
    if (!route.component && !route.children) {
      issues.push(`路由缺少component或children: ${route.path}`)
    }
    
    // 检查子路由
    if (route.children) {
      route.children.forEach(child => {
        if (!child.component) {
          issues.push(`子路由缺少component: ${route.path}/${child.path}`)
        }
      })
    }
  })
  
  return {
    isValid: issues.length === 0,
    issues
  }
}

/**
 * 获取标准化的菜单路由配置
 * @returns {Array} 标准化的路由配置
 */
export function getStandardMenuRoutes() {
  return [
    {
      path: '/index',
      name: 'Index',
      component: 'home/index',
      meta: { 
        title: '首页', 
        icon: 'dashboard',
        requiresAuth: false 
      }
    },
    {
      path: '/tools',
      name: 'Tools',
      component: 'tools/index',
      meta: { 
        title: '工具广场', 
        icon: 'tools',
        requiresAuth: false 
      }
    },
    {
      path: '/projects',
      name: 'Projects',
      redirect: '/projects/board',
      meta: { 
        title: '项目看板', 
        icon: 'kanban',
        requiresAuth: true,
        roles: ['project_manager', 'project_member']
      },
      children: [
        {
          path: 'board',
          name: 'ProjectsBoard',
          component: 'projects/board',
          meta: { 
            title: '项目看板', 
            icon: 'kanban',
            requiresAuth: true 
          }
        },
        {
          path: 'detail/:id',
          name: 'ProjectDetail',
          component: 'projects/detail',
          meta: { 
            title: '项目详情', 
            icon: 'kanban',
            requiresAuth: true 
          },
          hidden: true
        },
        {
          path: 'create-task/:projectId',
          name: 'CreateTask',
          component: 'projects/create-task',
          meta: { 
            title: '创建任务', 
            icon: 'kanban',
            requiresAuth: true 
          },
          hidden: true
        }
      ]
    },
    {
      path: '/knowledge-bases',
      name: 'KnowledgeBases',
      redirect: '/knowledge-bases/index',
      meta: { 
        title: '数据库管理', 
        icon: 'database',
        requiresAuth: true,
        roles: ['project_manager']
      },
      children: [
        {
          path: 'index',
          name: 'KnowledgeBasesIndex',
          component: 'knowledge-bases/index',
          meta: { 
            title: '数据库管理', 
            icon: 'database',
            requiresAuth: true 
          }
        },
        {
          path: 'detail/:id',
          name: 'KnowledgeBaseDetail',
          component: 'knowledge-bases/detail',
          meta: { 
            title: '数据库详情', 
            icon: 'database',
            requiresAuth: true 
          },
          hidden: true
        }
      ]
    }
  ]
}

/**
 * 检查路由是否存在
 * @param {String} path 路由路径
 * @returns {Boolean} 路由是否存在
 */
export function checkRouteExists(path) {
  const router = useRouter()
  try {
    const route = router.resolve(path)
    return route.name !== 'NotFound' && route.matched.length > 0
  } catch (error) {
    console.warn(`路由检查失败: ${path}`, error)
    return false
  }
}

/**
 * 修复菜单数据中的路径问题
 * @param {Array} menuData 菜单数据
 * @returns {Array} 修复后的菜单数据
 */
export function fixMenuData(menuData) {
  return menuData.map(menu => {
    // 修复路径映射
    const pathMapping = {
      'projects': '/projects',
      'knowledge-bases': '/knowledge-bases',
      'database': '/knowledge-bases', // 重定向到正确路径
      'tools': '/tools',
      'index': '/index'
    }
    
    if (pathMapping[menu.path]) {
      menu.path = pathMapping[menu.path]
    }
    
    // 移除重复的子菜单
    if (menu.children) {
      menu.children = menu.children.filter((child, index, arr) => {
        return arr.findIndex(c => c.path === child.path) === index
      })
    }
    
    return menu
  })
}
