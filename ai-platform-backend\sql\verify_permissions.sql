-- 权限系统验证SQL脚本
-- 用于验证权限配置是否正确
-- 执行时间: 2024年

-- ==========================================
-- 1. 验证表结构
-- ==========================================
SELECT '=== 表结构验证 ===' as section;

-- 检查项目成员表是否存在
SELECT 
    CASE 
        WHEN COUNT(*) > 0 THEN '✓ 项目成员表已创建'
        ELSE '✗ 项目成员表不存在'
    END as table_status
FROM information_schema.tables 
WHERE table_schema = DATABASE() AND table_name = 'rd_project_members';

-- 检查表字段
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_comment
FROM information_schema.columns 
WHERE table_schema = DATABASE() AND table_name = 'rd_project_members'
ORDER BY ordinal_position;

-- ==========================================
-- 2. 验证角色配置
-- ==========================================
SELECT '=== 角色配置验证 ===' as section;

-- 检查项目相关角色
SELECT 
    role_id,
    role_name,
    role_key,
    status,
    CASE 
        WHEN status = '0' THEN '✓ 启用'
        ELSE '✗ 禁用'
    END as status_desc
FROM sys_role 
WHERE role_key IN ('project_manager', 'project_member')
ORDER BY role_sort;

-- ==========================================
-- 3. 验证菜单权限配置
-- ==========================================
SELECT '=== 菜单权限验证 ===' as section;

-- 检查现有菜单的权限标识
SELECT 
    menu_id,
    menu_name,
    path,
    perms,
    CASE 
        WHEN perms IS NOT NULL AND perms != '' THEN '✓ 已配置权限'
        ELSE '✗ 缺少权限标识'
    END as permission_status
FROM sys_menu 
WHERE path IN ('tools', 'projects', 'knowledge-bases')
ORDER BY order_num;

-- ==========================================
-- 4. 验证角色菜单关联
-- ==========================================
SELECT '=== 角色菜单关联验证 ===' as section;

-- 项目管理员菜单权限
SELECT 
    '项目管理员' as role_name,
    m.menu_name,
    m.path,
    m.perms
FROM sys_role r
JOIN sys_role_menu rm ON r.role_id = rm.role_id
JOIN sys_menu m ON rm.menu_id = m.menu_id
WHERE r.role_key = 'project_manager'
ORDER BY m.order_num;

-- 项目成员菜单权限
SELECT 
    '项目成员' as role_name,
    m.menu_name,
    m.path,
    m.perms
FROM sys_role r
JOIN sys_role_menu rm ON r.role_id = rm.role_id
JOIN sys_menu m ON rm.menu_id = m.menu_id
WHERE r.role_key = 'project_member'
ORDER BY m.order_num;

-- 普通用户菜单权限
SELECT 
    '普通用户' as role_name,
    m.menu_name,
    m.path,
    m.perms
FROM sys_role r
JOIN sys_role_menu rm ON r.role_id = rm.role_id
JOIN sys_menu m ON rm.menu_id = m.menu_id
WHERE r.role_id = 2
ORDER BY m.order_num;

-- ==========================================
-- 5. 验证功能按钮权限
-- ==========================================
SELECT '=== 功能按钮权限验证 ===' as section;

-- 检查项目看板相关按钮权限
SELECT 
    parent.menu_name as parent_menu,
    child.menu_name as button_name,
    child.perms,
    child.menu_type
FROM sys_menu parent
JOIN sys_menu child ON parent.menu_id = child.parent_id
WHERE parent.path = 'projects' AND child.menu_type = 'F'
ORDER BY child.order_num;

-- 检查数据库管理相关按钮权限
SELECT 
    parent.menu_name as parent_menu,
    child.menu_name as button_name,
    child.perms,
    child.menu_type
FROM sys_menu parent
JOIN sys_menu child ON parent.menu_id = child.parent_id
WHERE parent.path = 'knowledge-bases' AND child.menu_type = 'F'
ORDER BY child.order_num;

-- 检查工具广场相关按钮权限
SELECT 
    parent.menu_name as parent_menu,
    child.menu_name as button_name,
    child.perms,
    child.menu_type
FROM sys_menu parent
JOIN sys_menu child ON parent.menu_id = child.parent_id
WHERE parent.path = 'tools' AND child.menu_type = 'F'
ORDER BY child.order_num;

-- ==========================================
-- 6. 权限配置统计
-- ==========================================
SELECT '=== 权限配置统计 ===' as section;

-- 角色统计
SELECT 
    '角色总数' as item,
    COUNT(*) as count
FROM sys_role 
WHERE status = '0'
UNION ALL
SELECT 
    '项目相关角色数' as item,
    COUNT(*) as count
FROM sys_role 
WHERE role_key IN ('project_manager', 'project_member') AND status = '0';

-- 菜单统计
SELECT 
    '主菜单数' as item,
    COUNT(*) as count
FROM sys_menu 
WHERE parent_id = 0 AND menu_type = 'M' AND status = '0'
UNION ALL
SELECT 
    '功能按钮数' as item,
    COUNT(*) as count
FROM sys_menu 
WHERE menu_type = 'F' AND perms LIKE 'business:%' AND status = '0';

-- 角色菜单关联统计
SELECT 
    CONCAT(r.role_name, '菜单权限数') as item,
    COUNT(*) as count
FROM sys_role r
JOIN sys_role_menu rm ON r.role_id = rm.role_id
WHERE r.role_key IN ('project_manager', 'project_member')
GROUP BY r.role_id, r.role_name;

-- ==========================================
-- 7. 潜在问题检查
-- ==========================================
SELECT '=== 潜在问题检查 ===' as section;

-- 检查是否有菜单缺少权限标识
SELECT 
    '缺少权限标识的菜单' as issue_type,
    menu_name,
    path
FROM sys_menu 
WHERE path IN ('tools', 'projects', 'knowledge-bases') 
AND (perms IS NULL OR perms = '')
AND status = '0';

-- 检查是否有角色没有分配菜单权限
SELECT 
    '没有菜单权限的角色' as issue_type,
    r.role_name,
    r.role_key
FROM sys_role r
LEFT JOIN sys_role_menu rm ON r.role_id = rm.role_id
WHERE r.role_key IN ('project_manager', 'project_member')
AND rm.role_id IS NULL
AND r.status = '0';

-- 检查项目成员表数据
SELECT 
    '项目成员数据统计' as info_type,
    COUNT(*) as total_members,
    COUNT(CASE WHEN role = 'manager' THEN 1 END) as managers,
    COUNT(CASE WHEN role = 'member' THEN 1 END) as members
FROM rd_project_members;
