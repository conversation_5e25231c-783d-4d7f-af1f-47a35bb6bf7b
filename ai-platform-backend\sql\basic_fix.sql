-- 最基础的菜单修复脚本
-- 只创建必要的主菜单，不添加功能按钮
-- 执行时间: 2024年

-- 清理现有业务菜单和权限
DELETE FROM sys_role_menu WHERE role_id IN (2, 100, 101);
DELETE FROM sys_menu WHERE menu_id >= 2000;

-- 创建工具广场菜单
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, menu_type, visible, status, perms, icon, create_by, create_time) 
VALUES (2000, '工具广场', 0, 1, 'tools', 'M', '0', '0', 'business:tools:list', 'tools', 'admin', NOW());

-- 创建项目看板菜单
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, menu_type, visible, status, perms, icon, create_by, create_time) 
VALUES (2001, '项目看板', 0, 2, 'projects', 'M', '0', '0', 'business:projects:list', 'kanban', 'admin', NOW());

-- 创建数据库管理菜单
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, menu_type, visible, status, perms, icon, create_by, create_time) 
VALUES (2002, '数据库管理', 0, 3, 'knowledge-bases', 'M', '0', '0', 'business:knowledge-bases:list', 'database', 'admin', NOW());

-- 配置普通用户权限（只能访问工具广场）
INSERT INTO sys_role_menu (role_id, menu_id) VALUES (2, 2000);

-- 配置项目管理员权限（可以访问所有菜单）
INSERT INTO sys_role_menu (role_id, menu_id) VALUES (100, 2000);
INSERT INTO sys_role_menu (role_id, menu_id) VALUES (100, 2001);
INSERT INTO sys_role_menu (role_id, menu_id) VALUES (100, 2002);

-- 配置项目成员权限（可以访问工具广场和项目看板）
INSERT INTO sys_role_menu (role_id, menu_id) VALUES (101, 2000);
INSERT INTO sys_role_menu (role_id, menu_id) VALUES (101, 2001);

-- 验证结果
SELECT '修复完成，当前主菜单:' as message;
SELECT menu_id, menu_name, path, perms FROM sys_menu WHERE menu_id IN (2000, 2001, 2002);

SELECT '角色权限配置:' as message;
SELECT r.role_name, m.menu_name FROM sys_role r
JOIN sys_role_menu rm ON r.role_id = rm.role_id
JOIN sys_menu m ON rm.menu_id = m.menu_id
WHERE r.role_id IN (2, 100, 101) AND m.menu_id >= 2000
ORDER BY r.role_id;
