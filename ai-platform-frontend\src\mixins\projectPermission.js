import { useProjectPermission } from '@/composables/useProjectPermission'

/**
 * 项目权限混入
 * 为组件提供权限相关的方法和计算属性
 */
export default {
  setup() {
    const {
      // 角色检查
      isAdmin,
      isProjectManager,
      isProjectMember,
      isCommonUser,
      
      // 权限检查
      hasProjectManagePermission,
      hasTaskManagePermission,
      hasDatabaseManagePermission,
      hasToolUsePermission,
      
      // 菜单检查（适配现有菜单结构）
      shouldShowHomeMenu,
      shouldShowToolsMenu,
      shouldShowProjectsMenu,
      shouldShowKnowledgeBasesMenu,
      shouldShowSystemMenu,
      
      // 用户信息
      userInfo,
      
      // 功能函数
      checkTaskPermission,
      checkProjectPermission,
      shouldShowButton,
      isTaskCreator,
      isProjectOwner,
      getAccessibleMenus,
      checkRoutePermission
    } = useProjectPermission()

    return {
      // 角色检查
      isAdmin,
      isProjectManager,
      isProjectMember,
      isCommonUser,
      
      // 权限检查
      hasProjectManagePermission,
      hasTaskManagePermission,
      hasDatabaseManagePermission,
      hasToolUsePermission,
      
      // 菜单检查（适配现有菜单结构）
      shouldShowHomeMenu,
      shouldShowToolsMenu,
      shouldShowProjectsMenu,
      shouldShowKnowledgeBasesMenu,
      shouldShowSystemMenu,
      
      // 用户信息
      userInfo,
      
      // 功能函数
      checkTaskPermission,
      checkProjectPermission,
      shouldShowButton,
      isTaskCreator,
      isProjectOwner,
      getAccessibleMenus,
      checkRoutePermission
    }
  },
  
  methods: {
    /**
     * 检查是否显示操作按钮
     * @param {String|Array} permissions 权限标识
     * @returns {Boolean}
     */
    $shouldShowButton(permissions) {
      return this.shouldShowButton(permissions)
    },
    
    /**
     * 检查任务权限
     * @param {Object} task 任务对象
     * @param {String} action 操作类型
     * @returns {Boolean}
     */
    $checkTaskPermission(task, action = 'view') {
      return this.checkTaskPermission(task, action)
    },
    
    /**
     * 检查项目权限
     * @param {Object} project 项目对象
     * @param {String} action 操作类型
     * @returns {Boolean}
     */
    $checkProjectPermission(project, action = 'view') {
      return this.checkProjectPermission(project, action)
    },
    
    /**
     * 检查是否是任务创建者
     * @param {Object} task 任务对象
     * @returns {Boolean}
     */
    $isTaskCreator(task) {
      return this.isTaskCreator(task)
    },
    
    /**
     * 检查是否是项目所有者
     * @param {Object} project 项目对象
     * @returns {Boolean}
     */
    $isProjectOwner(project) {
      return this.isProjectOwner(project)
    },
    
    /**
     * 获取用户可访问的菜单
     * @returns {Array}
     */
    $getAccessibleMenus() {
      return this.getAccessibleMenus()
    },
    
    /**
     * 检查路由权限
     * @param {Object} route 路由对象
     * @returns {Boolean}
     */
    $checkRoutePermission(route) {
      return this.checkRoutePermission(route)
    }
  }
}
