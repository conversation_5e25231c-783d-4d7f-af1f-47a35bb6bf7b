-- 完整修复菜单问题的SQL脚本
-- 彻底清理重复菜单，建立正确的扁平菜单结构
-- 执行时间: 2024年

-- ==========================================
-- 1. 备份当前配置（可选）
-- ==========================================
-- CREATE TABLE sys_menu_backup AS SELECT * FROM sys_menu WHERE menu_id >= 2000;
-- CREATE TABLE sys_role_menu_backup AS SELECT * FROM sys_role_menu WHERE role_id IN (2, 100, 101);

-- ==========================================
-- 2. 清理所有业务相关菜单和权限
-- ==========================================

-- 删除所有角色菜单关联（业务相关）
DELETE FROM sys_role_menu WHERE role_id IN (2, 100, 101);

-- 删除所有业务菜单（ID >= 2000）
DELETE FROM sys_menu WHERE menu_id >= 2000;

-- ==========================================
-- 3. 重新创建正确的扁平菜单结构
-- ==========================================

-- 创建工具广场菜单
INSERT INTO sys_menu (
    menu_id, menu_name, parent_id, order_num, path, component, 
    menu_type, visible, status, perms, icon, create_by, create_time
) VALUES (
    2000, '工具广场', 0, 1, 'tools', NULL, 
    'M', '0', '0', 'business:tools:list', 'tools', 'admin', NOW()
);

-- 创建项目看板菜单（注意：菜单名改为"项目看板"，路径为"projects"）
INSERT INTO sys_menu (
    menu_id, menu_name, parent_id, order_num, path, component, 
    menu_type, visible, status, perms, icon, create_by, create_time
) VALUES (
    2001, '项目看板', 0, 2, 'projects', NULL, 
    'M', '0', '0', 'business:projects:list', 'kanban', 'admin', NOW()
);

-- 创建数据库管理菜单（路径为"knowledge-bases"）
INSERT INTO sys_menu (
    menu_id, menu_name, parent_id, order_num, path, component, 
    menu_type, visible, status, perms, icon, create_by, create_time
) VALUES (
    2002, '数据库管理', 0, 3, 'knowledge-bases', NULL, 
    'M', '0', '0', 'business:knowledge-bases:list', 'database', 'admin', NOW()
);

-- ==========================================
-- 4. 添加功能按钮权限（可选，用于细粒度控制）
-- ==========================================

-- 工具广场功能按钮
INSERT INTO sys_menu (menu_name, parent_id, order_num, menu_type, visible, status, perms, icon, create_by, create_time) VALUES
('工具查询', 2000, 1, 'F', '0', '0', 'business:tools:query', '#', 'admin', NOW()),
('工具使用', 2000, 2, 'F', '0', '0', 'business:tools:use', '#', 'admin', NOW());

-- 项目看板功能按钮
INSERT INTO sys_menu (menu_name, parent_id, order_num, menu_type, visible, status, perms, icon, create_by, create_time) VALUES
('项目查询', 2001, 1, 'F', '0', '0', 'business:projects:query', '#', 'admin', NOW()),
('项目新增', 2001, 2, 'F', '0', '0', 'business:projects:add', '#', 'admin', NOW()),
('项目修改', 2001, 3, 'F', '0', '0', 'business:projects:edit', '#', 'admin', NOW()),
('项目删除', 2001, 4, 'F', '0', '0', 'business:projects:remove', '#', 'admin', NOW()),
('任务查询', 2001, 5, 'F', '0', '0', 'business:tasks:query', '#', 'admin', NOW()),
('任务新增', 2001, 6, 'F', '0', '0', 'business:tasks:add', '#', 'admin', NOW()),
('任务修改', 2001, 7, 'F', '0', '0', 'business:tasks:edit', '#', 'admin', NOW'),
('任务删除', 2001, 8, 'F', '0', '0', 'business:tasks:remove', '#', 'admin', NOW());

-- 数据库管理功能按钮
INSERT INTO sys_menu (menu_name, parent_id, order_num, menu_type, visible, status, perms, icon, create_by, create_time) VALUES
('知识库查询', 2002, 1, 'F', '0', '0', 'business:knowledge-bases:query', '#', 'admin', NOW()),
('知识库新增', 2002, 2, 'F', '0', '0', 'business:knowledge-bases:add', '#', 'admin', NOW()),
('知识库修改', 2002, 3, 'F', '0', '0', 'business:knowledge-bases:edit', '#', 'admin', NOW()),
('知识库删除', 2002, 4, 'F', '0', '0', 'business:knowledge-bases:remove', '#', 'admin', NOW());

-- ==========================================
-- 5. 重新配置角色菜单权限
-- ==========================================

-- 普通用户权限 (role_id = 2)
-- 只能访问工具广场
INSERT INTO sys_role_menu (role_id, menu_id) VALUES (2, 2000);

-- 项目管理员权限 (role_id = 100)
-- 可以访问：工具广场 + 项目看板 + 数据库管理
INSERT INTO sys_role_menu (role_id, menu_id) VALUES 
(100, 2000),
(100, 2001), 
(100, 2002);

-- 项目成员权限 (role_id = 101)
-- 可以访问：工具广场 + 项目看板
INSERT INTO sys_role_menu (role_id, menu_id) VALUES 
(101, 2000),
(101, 2001);

-- ==========================================
-- 6. 验证修复结果
-- ==========================================

SELECT '=== 修复后的主菜单 ===' as section;
SELECT 
    menu_id,
    menu_name,
    path,
    perms,
    icon,
    order_num
FROM sys_menu 
WHERE parent_id = 0 AND menu_type = 'M' AND menu_id >= 2000
ORDER BY order_num;

SELECT '=== 功能按钮权限 ===' as section;
SELECT 
    parent.menu_name as parent_menu,
    child.menu_name as button_name,
    child.perms
FROM sys_menu parent
JOIN sys_menu child ON parent.menu_id = child.parent_id
WHERE parent.menu_id >= 2000 AND child.menu_type = 'F'
ORDER BY parent.menu_id, child.order_num;

SELECT '=== 角色菜单权限 ===' as section;
SELECT 
    r.role_name,
    m.menu_name,
    m.path,
    m.perms
FROM sys_role r
JOIN sys_role_menu rm ON r.role_id = rm.role_id
JOIN sys_menu m ON rm.menu_id = m.menu_id
WHERE r.role_id IN (2, 100, 101) AND m.menu_id >= 2000
ORDER BY r.role_id, m.order_num;

SELECT '=== 检查重复菜单 ===' as section;
SELECT 
    menu_name,
    COUNT(*) as count,
    GROUP_CONCAT(menu_id) as menu_ids
FROM sys_menu 
WHERE menu_id >= 2000 AND status = '0'
GROUP BY menu_name 
HAVING COUNT(*) > 1;

SELECT '=== 检查子菜单 ===' as section;
SELECT 
    COUNT(*) as child_menu_count,
    CASE 
        WHEN COUNT(*) = 0 THEN '✓ 无子菜单，结构正确'
        ELSE CONCAT('⚠ 发现 ', COUNT(*), ' 个子菜单')
    END as status
FROM sys_menu 
WHERE parent_id >= 2000 AND menu_type = 'C' AND status = '0';

COMMIT;
