-- 简化版菜单修复脚本
-- 分步骤执行，避免复杂语法
-- 执行时间: 2024年

-- ==========================================
-- 步骤1: 清理现有业务菜单
-- ==========================================

-- 删除角色菜单关联
DELETE FROM sys_role_menu WHERE role_id IN (2, 100, 101);

-- 删除所有业务菜单
DELETE FROM sys_menu WHERE menu_id >= 2000;

-- ==========================================
-- 步骤2: 创建工具广场菜单
-- ==========================================
INSERT INTO sys_menu (
    menu_id, menu_name, parent_id, order_num, path, component, 
    menu_type, visible, status, perms, icon, create_by, create_time
) VALUES (
    2000, '工具广场', 0, 1, 'tools', NULL, 
    'M', '0', '0', 'business:tools:list', 'tools', 'admin', NOW()
);

-- ==========================================
-- 步骤3: 创建项目看板菜单
-- ==========================================
INSERT INTO sys_menu (
    menu_id, menu_name, parent_id, order_num, path, component, 
    menu_type, visible, status, perms, icon, create_by, create_time
) VALUES (
    2001, '项目看板', 0, 2, 'projects', NULL, 
    'M', '0', '0', 'business:projects:list', 'kanban', 'admin', NOW()
);

-- ==========================================
-- 步骤4: 创建数据库管理菜单
-- ==========================================
INSERT INTO sys_menu (
    menu_id, menu_name, parent_id, order_num, path, component, 
    menu_type, visible, status, perms, icon, create_by, create_time
) VALUES (
    2002, '数据库管理', 0, 3, 'knowledge-bases', NULL, 
    'M', '0', '0', 'business:knowledge-bases:list', 'database', 'admin', NOW()
);

-- ==========================================
-- 步骤5: 添加工具广场功能按钮
-- ==========================================
INSERT INTO sys_menu (menu_name, parent_id, order_num, menu_type, visible, status, perms, icon, create_by, create_time) 
VALUES ('工具查询', 2000, 1, 'F', '0', '0', 'business:tools:query', '#', 'admin', NOW());

INSERT INTO sys_menu (menu_name, parent_id, order_num, menu_type, visible, status, perms, icon, create_by, create_time) 
VALUES ('工具使用', 2000, 2, 'F', '0', '0', 'business:tools:use', '#', 'admin', NOW());

-- ==========================================
-- 步骤6: 添加项目看板功能按钮
-- ==========================================
INSERT INTO sys_menu (menu_name, parent_id, order_num, menu_type, visible, status, perms, icon, create_by, create_time) 
VALUES ('项目查询', 2001, 1, 'F', '0', '0', 'business:projects:query', '#', 'admin', NOW());

INSERT INTO sys_menu (menu_name, parent_id, order_num, menu_type, visible, status, perms, icon, create_by, create_time) 
VALUES ('项目新增', 2001, 2, 'F', '0', '0', 'business:projects:add', '#', 'admin', NOW());

INSERT INTO sys_menu (menu_name, parent_id, order_num, menu_type, visible, status, perms, icon, create_by, create_time) 
VALUES ('项目修改', 2001, 3, 'F', '0', '0', 'business:projects:edit', '#', 'admin', NOW());

INSERT INTO sys_menu (menu_name, parent_id, order_num, menu_type, visible, status, perms, icon, create_by, create_time) 
VALUES ('项目删除', 2001, 4, 'F', '0', '0', 'business:projects:remove', '#', 'admin', NOW());

INSERT INTO sys_menu (menu_name, parent_id, order_num, menu_type, visible, status, perms, icon, create_by, create_time) 
VALUES ('任务查询', 2001, 5, 'F', '0', '0', 'business:tasks:query', '#', 'admin', NOW());

INSERT INTO sys_menu (menu_name, parent_id, order_num, menu_type, visible, status, perms, icon, create_by, create_time) 
VALUES ('任务新增', 2001, 6, 'F', '0', '0', 'business:tasks:add', '#', 'admin', NOW());

INSERT INTO sys_menu (menu_name, parent_id, order_num, menu_type, visible, status, perms, icon, create_by, create_time) 
VALUES ('任务修改', 2001, 7, 'F', '0', '0', 'business:tasks:edit', '#', 'admin', NOW());

INSERT INTO sys_menu (menu_name, parent_id, order_num, menu_type, visible, status, perms, icon, create_by, create_time) 
VALUES ('任务删除', 2001, 8, 'F', '0', '0', 'business:tasks:remove', '#', 'admin', NOW());

-- ==========================================
-- 步骤7: 添加数据库管理功能按钮
-- ==========================================
INSERT INTO sys_menu (menu_name, parent_id, order_num, menu_type, visible, status, perms, icon, create_by, create_time) 
VALUES ('知识库查询', 2002, 1, 'F', '0', '0', 'business:knowledge-bases:query', '#', 'admin', NOW());

INSERT INTO sys_menu (menu_name, parent_id, order_num, menu_type, visible, status, perms, icon, create_by, create_time) 
VALUES ('知识库新增', 2002, 2, 'F', '0', '0', 'business:knowledge-bases:add', '#', 'admin', NOW());

INSERT INTO sys_menu (menu_name, parent_id, order_num, menu_type, visible, status, perms, icon, create_by, create_time) 
VALUES ('知识库修改', 2002, 3, 'F', '0', '0', 'business:knowledge-bases:edit', '#', 'admin', NOW());

INSERT INTO sys_menu (menu_name, parent_id, order_num, menu_type, visible, status, perms, icon, create_by, create_time) 
VALUES ('知识库删除', 2002, 4, 'F', '0', '0', 'business:knowledge-bases:remove', '#', 'admin', NOW());

-- ==========================================
-- 步骤8: 配置普通用户权限
-- ==========================================
INSERT INTO sys_role_menu (role_id, menu_id) VALUES (2, 2000);

-- ==========================================
-- 步骤9: 配置项目管理员权限
-- ==========================================
INSERT INTO sys_role_menu (role_id, menu_id) VALUES (100, 2000);
INSERT INTO sys_role_menu (role_id, menu_id) VALUES (100, 2001);
INSERT INTO sys_role_menu (role_id, menu_id) VALUES (100, 2002);

-- ==========================================
-- 步骤10: 配置项目成员权限
-- ==========================================
INSERT INTO sys_role_menu (role_id, menu_id) VALUES (101, 2000);
INSERT INTO sys_role_menu (role_id, menu_id) VALUES (101, 2001);

-- ==========================================
-- 验证结果
-- ==========================================
SELECT '=== 修复后的主菜单 ===' as section;
SELECT menu_id, menu_name, path, perms, icon FROM sys_menu WHERE menu_id IN (2000, 2001, 2002) ORDER BY menu_id;

SELECT '=== 角色权限配置 ===' as section;
SELECT r.role_name, m.menu_name, m.path FROM sys_role r
JOIN sys_role_menu rm ON r.role_id = rm.role_id
JOIN sys_menu m ON rm.menu_id = m.menu_id
WHERE r.role_id IN (2, 100, 101) AND m.menu_id >= 2000
ORDER BY r.role_id, m.menu_id;

COMMIT;
