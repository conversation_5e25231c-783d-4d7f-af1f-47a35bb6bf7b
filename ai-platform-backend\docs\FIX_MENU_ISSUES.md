# 菜单路由问题修复指南

## 问题描述

根据您的反馈，存在以下问题：

1. **项目看板404**: `http://localhost:3000/projects/board` 无法访问
2. **项目管理菜单重复**: 存在不应该有的"项目管理"菜单和子菜单 `http://localhost:3000/projects/list`
3. **数据库管理重复**: 
   - 正常的：`http://localhost:3000/knowledge-bases/index`
   - 异常的：`http://localhost:3000/database/knowledge-bases`（含子菜单）

## 解决方案

### 步骤1：诊断当前问题

```bash
# 执行诊断脚本，查看当前菜单配置
mysql -u username -p database_name < sql/check_current_menus.sql
```

### 步骤2：修复数据库菜单配置

```bash
# 执行修复脚本
mysql -u username -p database_name < sql/fix_menu_routes.sql
```

### 步骤3：清理前端缓存

```bash
# 清理前端缓存和重新构建
cd ai-platform-frontend
rm -rf node_modules/.cache
rm -rf dist
npm run build:dev
```

### 步骤4：重启服务

```bash
# 重启后端服务
cd ai-platform-backend
python server.py

# 重启前端服务
cd ai-platform-frontend
npm run dev
```

## 预期结果

修复后应该只有以下菜单：

1. **首页** (`/index`)
2. **工具广场** (`/tools`) 
3. **项目看板** (`/projects`) - 直接跳转到 `/projects/board`
4. **数据库管理** (`/knowledge-bases`) - 直接跳转到 `/knowledge-bases/index`

## 手动修复（如果脚本执行失败）

### 1. 删除重复菜单

```sql
-- 删除错误的项目管理菜单
DELETE FROM sys_menu WHERE menu_name = '项目管理' AND path != 'projects';
DELETE FROM sys_menu WHERE menu_name = '项目列表';

-- 删除错误的数据库管理菜单
DELETE FROM sys_menu WHERE path = 'database';
DELETE FROM sys_menu WHERE path = 'database/knowledge-bases';

-- 删除所有子菜单（保持扁平结构）
DELETE FROM sys_menu WHERE parent_id IN (
    SELECT menu_id FROM (
        SELECT menu_id FROM sys_menu WHERE path IN ('projects', 'knowledge-bases')
    ) as temp
) AND menu_type = 'C';
```

### 2. 确保正确的菜单配置

```sql
-- 确保项目看板菜单正确
UPDATE sys_menu SET 
    menu_name = '项目看板',
    path = 'projects',
    perms = 'business:projects:list',
    icon = 'kanban',
    menu_type = 'M'
WHERE path = 'projects' AND parent_id = 0;

-- 确保数据库管理菜单正确
UPDATE sys_menu SET 
    menu_name = '数据库管理',
    path = 'knowledge-bases',
    perms = 'business:knowledge-bases:list',
    icon = 'database',
    menu_type = 'M'
WHERE path = 'knowledge-bases' AND parent_id = 0;
```

### 3. 重新配置角色权限

```sql
-- 获取菜单ID
SET @projects_menu_id = (SELECT menu_id FROM sys_menu WHERE path = 'projects' AND parent_id = 0);
SET @kb_menu_id = (SELECT menu_id FROM sys_menu WHERE path = 'knowledge-bases' AND parent_id = 0);
SET @tools_menu_id = (SELECT menu_id FROM sys_menu WHERE path = 'tools' AND parent_id = 0);

-- 项目管理员权限
DELETE FROM sys_role_menu WHERE role_id = 100;
INSERT INTO sys_role_menu (role_id, menu_id) VALUES
(100, @tools_menu_id),
(100, @projects_menu_id),
(100, @kb_menu_id);

-- 项目成员权限
DELETE FROM sys_role_menu WHERE role_id = 101;
INSERT INTO sys_role_menu (role_id, menu_id) VALUES
(101, @tools_menu_id),
(101, @projects_menu_id);
```

## 验证修复

### 1. 数据库验证

```sql
-- 检查主菜单
SELECT menu_name, path, perms, icon FROM sys_menu 
WHERE parent_id = 0 AND menu_type = 'M' AND status = '0'
ORDER BY order_num;

-- 检查是否还有子菜单
SELECT COUNT(*) as child_menu_count FROM sys_menu 
WHERE parent_id != 0 AND menu_type = 'C' AND status = '0';

-- 检查角色权限
SELECT r.role_name, m.menu_name, m.path 
FROM sys_role r
JOIN sys_role_menu rm ON r.role_id = rm.role_id
JOIN sys_menu m ON rm.menu_id = m.menu_id
WHERE r.role_id IN (100, 101) AND m.status = '0';
```

### 2. 前端验证

1. 清除浏览器缓存
2. 重新登录系统
3. 检查菜单显示：
   - 应该只有4个主菜单（首页、工具广场、项目看板、数据库管理）
   - 没有子菜单展开
   - 没有重复菜单

4. 测试路由访问：
   - `/projects` 应该重定向到 `/projects/board`
   - `/projects/board` 应该正常显示项目看板
   - `/knowledge-bases` 应该重定向到 `/knowledge-bases/index`
   - `/knowledge-bases/index` 应该正常显示数据库管理

## 常见问题

### Q: 修复后仍然看到重复菜单？
A: 
1. 清除浏览器缓存
2. 检查是否有前端路由缓存
3. 重启前后端服务

### Q: 项目看板仍然404？
A: 
1. 检查前端路由配置是否正确
2. 确认 `projects/board.vue` 组件文件存在
3. 检查路由权限配置

### Q: 数据库管理访问异常？
A: 
1. 确认用户有项目管理员角色
2. 检查 `knowledge-bases/index.vue` 组件文件存在
3. 验证后端API路径是否正确

## 预防措施

1. **不要手动添加菜单** - 只使用现有的4个主菜单
2. **避免创建子菜单** - 保持扁平的菜单结构
3. **统一路径命名** - 使用 `projects` 和 `knowledge-bases`，避免 `database`
4. **定期检查** - 使用诊断脚本定期检查菜单配置

## 技术支持

如果问题仍然存在，请提供：
1. 诊断脚本的输出结果
2. 浏览器控制台的错误信息
3. 后端服务的日志信息
4. 当前的菜单显示截图
