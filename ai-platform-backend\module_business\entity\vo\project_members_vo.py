from datetime import datetime
from pydantic import BaseModel, ConfigDict, Field
from pydantic.alias_generators import to_camel
from pydantic_validation_decorator import NotBlank
from typing import Optional
from module_admin.annotation.pydantic_annotation import as_query


class ProjectMembersModel(BaseModel):
    """
    项目成员关联表对应pydantic模型
    """
    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True)

    project_id: Optional[int] = Field(default=None, description='项目ID')
    user_id: Optional[int] = Field(default=None, description='用户ID')
    role: Optional[str] = Field(default='member', description='项目角色：manager(项目管理员), member(项目成员)')
    assigned_at: Optional[datetime] = Field(default=None, description='分配时间')
    assigned_by: Optional[int] = Field(default=None, description='分配者ID')

    @NotBlank(field_name='project_id', message='项目ID不能为空')
    def get_project_id(self):
        return self.project_id

    @NotBlank(field_name='user_id', message='用户ID不能为空')
    def get_user_id(self):
        return self.user_id

    @NotBlank(field_name='role', message='项目角色不能为空')
    def get_role(self):
        return self.role

    def validate_fields(self):
        self.get_project_id()
        self.get_user_id()
        self.get_role()


@as_query
class ProjectMembersPageQueryModel(ProjectMembersModel):
    """
    项目成员关联表分页查询模型
    """
    page_num: int = Field(default=1, description='当前页码')
    page_size: int = Field(default=10, description='每页记录数')
    begin_time: Optional[str] = Field(default=None, description='开始时间')
    end_time: Optional[str] = Field(default=None, description='结束时间')


class ProjectMembersQueryModel(ProjectMembersModel):
    """
    项目成员关联表不分页查询模型
    """
    pass


class DeleteProjectMembersModel(BaseModel):
    """
    删除项目成员模型
    """
    model_config = ConfigDict(alias_generator=to_camel)

    project_ids: str = Field(description='需要删除的项目ID')
    user_ids: str = Field(description='需要删除的用户ID')


class ProjectMemberWithUserInfoModel(BaseModel):
    """
    项目成员信息（包含用户详细信息）
    """
    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True)

    project_id: Optional[int] = Field(default=None, description='项目ID')
    user_id: Optional[int] = Field(default=None, description='用户ID')
    role: Optional[str] = Field(default='member', description='项目角色')
    assigned_at: Optional[datetime] = Field(default=None, description='分配时间')
    assigned_by: Optional[int] = Field(default=None, description='分配者ID')
    
    # 用户信息
    user_name: Optional[str] = Field(default=None, description='用户账号')
    nick_name: Optional[str] = Field(default=None, description='用户昵称')
    email: Optional[str] = Field(default=None, description='用户邮箱')
    phonenumber: Optional[str] = Field(default=None, description='手机号码')
    dept_name: Optional[str] = Field(default=None, description='部门名称')
