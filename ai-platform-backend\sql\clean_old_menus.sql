-- 清理旧菜单系统的SQL脚本
-- 删除之前创建的重复菜单，只保留现在修复后的菜单结构
-- 执行时间: 2024年

-- ==========================================
-- 1. 查看当前所有菜单（用于确认）
-- ==========================================
SELECT '=== 当前所有主菜单 ===' as section;
SELECT menu_id, menu_name, parent_id, path, menu_type, visible, status 
FROM sys_menu 
WHERE parent_id = 0 AND menu_type = 'M' 
ORDER BY order_num;

-- ==========================================
-- 2. 删除系统默认的重复菜单
-- ==========================================

-- 删除可能存在的重复系统菜单（保留ID小于2000的系统菜单）
-- 但删除可能与业务菜单冲突的菜单

-- 检查是否有与业务菜单路径冲突的系统菜单
SELECT '=== 检查路径冲突的系统菜单 ===' as section;
SELECT menu_id, menu_name, path, menu_type 
FROM sys_menu 
WHERE menu_id < 2000 
AND path IN ('tools', 'projects', 'knowledge-bases', 'database')
AND status = '0';

-- 如果存在冲突的系统菜单，将其禁用而不是删除（保持数据完整性）
UPDATE sys_menu 
SET status = '1', visible = '1'
WHERE menu_id < 2000 
AND path IN ('tools', 'projects', 'knowledge-bases', 'database')
AND status = '0';

-- ==========================================
-- 3. 清理可能存在的重复业务菜单
-- ==========================================

-- 删除ID大于2002的重复业务菜单（保留2000, 2001, 2002）
DELETE FROM sys_role_menu 
WHERE menu_id > 2002 AND menu_id < 3000;

DELETE FROM sys_menu 
WHERE menu_id > 2002 AND menu_id < 3000;

-- ==========================================
-- 4. 确保业务菜单的唯一性和正确性
-- ==========================================

-- 检查是否有重复的业务菜单名称
SELECT '=== 检查重复菜单名称 ===' as section;
SELECT menu_name, COUNT(*) as count, GROUP_CONCAT(menu_id) as menu_ids
FROM sys_menu 
WHERE status = '0' AND menu_type = 'M'
GROUP BY menu_name 
HAVING COUNT(*) > 1;

-- 如果发现重复，保留ID最小的，删除其他的
-- 这里我们手动处理，因为要保留我们刚创建的正确菜单

-- 删除可能存在的旧版本工具广场菜单（保留menu_id=2000的）
DELETE rm FROM sys_role_menu rm
JOIN sys_menu m ON rm.menu_id = m.menu_id
WHERE m.menu_name = '工具广场' AND m.menu_id != 2000;

DELETE FROM sys_menu 
WHERE menu_name = '工具广场' AND menu_id != 2000;

-- 删除可能存在的旧版本项目相关菜单（保留menu_id=2001的项目看板）
DELETE rm FROM sys_role_menu rm
JOIN sys_menu m ON rm.menu_id = m.menu_id
WHERE (m.menu_name LIKE '%项目%' OR m.path LIKE '%project%') AND m.menu_id != 2001;

DELETE FROM sys_menu 
WHERE (menu_name LIKE '%项目%' OR path LIKE '%project%') AND menu_id != 2001;

-- 删除可能存在的旧版本数据库管理菜单（保留menu_id=2002的）
DELETE rm FROM sys_role_menu rm
JOIN sys_menu m ON rm.menu_id = m.menu_id
WHERE (m.menu_name LIKE '%数据库%' OR m.path LIKE '%database%' OR m.path LIKE '%knowledge%') AND m.menu_id != 2002;

DELETE FROM sys_menu 
WHERE (menu_name LIKE '%数据库%' OR path LIKE '%database%' OR path LIKE '%knowledge%') AND menu_id != 2002;

-- ==========================================
-- 5. 清理无效的角色菜单关联
-- ==========================================

-- 删除指向不存在菜单的角色菜单关联
DELETE rm FROM sys_role_menu rm 
LEFT JOIN sys_menu m ON rm.menu_id = m.menu_id 
WHERE m.menu_id IS NULL;

-- 删除指向已禁用菜单的角色菜单关联
DELETE rm FROM sys_role_menu rm 
JOIN sys_menu m ON rm.menu_id = m.menu_id 
WHERE m.status = '1';

-- ==========================================
-- 6. 验证清理结果
-- ==========================================

SELECT '=== 清理后的主菜单 ===' as section;
SELECT menu_id, menu_name, path, perms, icon, status
FROM sys_menu 
WHERE parent_id = 0 AND menu_type = 'M' AND status = '0'
ORDER BY order_num;

SELECT '=== 业务菜单验证 ===' as section;
SELECT 
    menu_id,
    menu_name,
    path,
    CASE 
        WHEN menu_id = 2000 AND menu_name = '工具广场' AND path = 'tools' THEN '✓ 正确'
        WHEN menu_id = 2001 AND menu_name = '项目看板' AND path = 'projects' THEN '✓ 正确'
        WHEN menu_id = 2002 AND menu_name = '数据库管理' AND path = 'knowledge-bases' THEN '✓ 正确'
        ELSE '需要检查'
    END as status
FROM sys_menu 
WHERE menu_id IN (2000, 2001, 2002);

SELECT '=== 角色权限验证 ===' as section;
SELECT r.role_name, COUNT(rm.menu_id) as menu_count, GROUP_CONCAT(m.menu_name) as menus
FROM sys_role r
LEFT JOIN sys_role_menu rm ON r.role_id = rm.role_id
LEFT JOIN sys_menu m ON rm.menu_id = m.menu_id AND m.status = '0'
WHERE r.role_id IN (2, 100, 101)
GROUP BY r.role_id, r.role_name
ORDER BY r.role_id;

SELECT '=== 检查是否还有重复菜单 ===' as section;
SELECT 
    CASE 
        WHEN COUNT(*) = 0 THEN '✓ 无重复菜单'
        ELSE CONCAT('⚠ 仍有 ', COUNT(*), ' 组重复菜单')
    END as duplicate_status
FROM (
    SELECT menu_name
    FROM sys_menu 
    WHERE status = '0' AND menu_type = 'M'
    GROUP BY menu_name 
    HAVING COUNT(*) > 1
) as duplicates;

COMMIT;
