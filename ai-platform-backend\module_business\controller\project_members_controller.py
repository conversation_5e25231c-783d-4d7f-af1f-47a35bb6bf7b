from fastapi import APIRouter, Depends, Request, Form
from pydantic_validation_decorator import Validate<PERSON>ields
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List
from config.enums import BusinessType
from config.get_db import get_db
from module_admin.annotation.log_annotation import Log
from module_admin.aspect.interface_auth import CheckUserInterfaceAuth
from module_admin.entity.vo.user_vo import CurrentUserModel
from module_admin.service.login_service import LoginService
from module_business.service.project_members_service import ProjectMembersService
from module_business.entity.vo.project_members_vo import (
    ProjectMembersModel,
    ProjectMembersPageQueryModel,
    DeleteProjectMembersModel
)
from utils.log_util import logger
from utils.page_util import PageResponseModel
from utils.response_util import ResponseUtil
from datetime import datetime


projectMembersController = APIRouter(
    prefix='/business/project-members', 
    dependencies=[Depends(LoginService.get_current_user)]
)


@projectMembersController.get(
    '/list', 
    response_model=PageResponseModel,
    dependencies=[Depends(CheckUserInterfaceAuth('business:project-members:list'))]
)
async def get_business_project_members_list(
    request: Request,
    project_members_page_query: ProjectMembersPageQueryModel = Depends(),
    query_db: AsyncSession = Depends(get_db),
):
    """
    获取项目成员列表
    """
    # 获取分页数据
    project_members_page_query_result = await ProjectMembersService.get_project_members_list_services(
        query_db, project_members_page_query, is_page=True
    )
    logger.info('获取成功')

    return ResponseUtil.success(model_content=project_members_page_query_result)


@projectMembersController.post(
    '', 
    dependencies=[Depends(CheckUserInterfaceAuth('business:project-members:add'))]
)
@ValidateFields(validate_model='add_project_members')
@Log(title='项目成员管理', business_type=BusinessType.INSERT)
async def add_business_project_member(
    request: Request,
    add_project_member: ProjectMembersModel,
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    新增项目成员
    """
    add_project_member.assigned_at = datetime.now()
    add_project_member.assigned_by = current_user.user.user_id
    
    add_project_member_result = await ProjectMembersService.add_project_member_services(
        query_db, add_project_member
    )
    logger.info(add_project_member_result.message)

    return ResponseUtil.success(msg=add_project_member_result.message)


@projectMembersController.put(
    '', 
    dependencies=[Depends(CheckUserInterfaceAuth('business:project-members:edit'))]
)
@ValidateFields(validate_model='edit_project_members')
@Log(title='项目成员管理', business_type=BusinessType.UPDATE)
async def edit_business_project_member(
    request: Request,
    edit_project_member: ProjectMembersModel,
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    编辑项目成员
    """
    edit_project_member_result = await ProjectMembersService.edit_project_member_services(
        query_db, edit_project_member
    )
    logger.info(edit_project_member_result.message)

    return ResponseUtil.success(msg=edit_project_member_result.message)


@projectMembersController.delete(
    '', 
    dependencies=[Depends(CheckUserInterfaceAuth('business:project-members:remove'))]
)
@Log(title='项目成员管理', business_type=BusinessType.DELETE)
async def delete_business_project_member(
    request: Request,
    delete_project_member: DeleteProjectMembersModel = Depends(DeleteProjectMembersModel.as_query),
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    删除项目成员
    """
    delete_project_member_result = await ProjectMembersService.delete_project_member_services(
        query_db, delete_project_member
    )
    logger.info(delete_project_member_result.message)

    return ResponseUtil.success(msg=delete_project_member_result.message)


@projectMembersController.get(
    '/{project_id}/{user_id}', 
    response_model=ProjectMembersModel,
    dependencies=[Depends(CheckUserInterfaceAuth('business:project-members:query'))]
)
async def query_detail_business_project_member(
    request: Request, 
    project_id: int, 
    user_id: int,
    query_db: AsyncSession = Depends(get_db),
):
    """
    获取项目成员详情
    """
    project_member_detail_result = await ProjectMembersService.project_member_detail_services(
        query_db, project_id, user_id
    )
    logger.info(f'获取project_id为{project_id}, user_id为{user_id}的项目成员信息成功')

    return ResponseUtil.success(data=project_member_detail_result)


@projectMembersController.get(
    '/user-projects/{user_id}',
    dependencies=[Depends(CheckUserInterfaceAuth('business:project-members:query'))]
)
async def get_user_projects(
    request: Request,
    user_id: int,
    query_db: AsyncSession = Depends(get_db),
):
    """
    获取用户参与的所有项目
    """
    user_projects_result = await ProjectMembersService.get_user_projects_services(query_db, user_id)
    logger.info(f'获取用户{user_id}的项目列表成功')

    return ResponseUtil.success(data=user_projects_result)


@projectMembersController.get(
    '/project-managers/{project_id}',
    dependencies=[Depends(CheckUserInterfaceAuth('business:project-members:query'))]
)
async def get_project_managers(
    request: Request,
    project_id: int,
    query_db: AsyncSession = Depends(get_db),
):
    """
    获取项目管理员列表
    """
    project_managers_result = await ProjectMembersService.get_project_managers_services(query_db, project_id)
    logger.info(f'获取项目{project_id}的管理员列表成功')

    return ResponseUtil.success(data=project_managers_result)


@projectMembersController.post(
    '/batch-add',
    dependencies=[Depends(CheckUserInterfaceAuth('business:project-members:add'))]
)
@Log(title='项目成员管理', business_type=BusinessType.INSERT)
async def batch_add_project_members(
    request: Request,
    project_id: int = Form(..., description="项目ID"),
    user_ids: str = Form(..., description="用户ID列表，逗号分隔"),
    role: str = Form(default="member", description="项目角色"),
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    批量添加项目成员
    """
    try:
        user_id_list = [int(uid.strip()) for uid in user_ids.split(',') if uid.strip()]
        
        batch_add_result = await ProjectMembersService.batch_add_project_members_services(
            query_db, project_id, user_id_list, role, current_user.user.user_id
        )
        logger.info(batch_add_result.message)

        return ResponseUtil.success(msg=batch_add_result.message)
    except Exception as e:
        logger.error(f'批量添加项目成员失败: {e}')
        return ResponseUtil.error(msg=f'批量添加项目成员失败: {str(e)}')


@projectMembersController.get(
    '/check-permission/{user_id}/{project_id}',
    dependencies=[Depends(CheckUserInterfaceAuth('business:project-members:query'))]
)
async def check_user_project_permission(
    request: Request,
    user_id: int,
    project_id: int,
    required_role: str = None,
    query_db: AsyncSession = Depends(get_db),
):
    """
    检查用户项目权限
    """
    has_permission = await ProjectMembersService.check_user_project_permission_services(
        query_db, user_id, project_id, required_role
    )
    
    return ResponseUtil.success(data={
        'has_permission': has_permission,
        'user_id': user_id,
        'project_id': project_id,
        'required_role': required_role
    })
