<template>
  <div class="tools-container">
    <div class="tools-header">
      <h1>工具广场</h1>
      <p>共 {{ tools.length }} 个工具，显示 {{ filteredTools.length }} 个</p>
    </div>

    <!-- 搜索和筛选 -->
    <div class="tools-filter">
      <el-input
        v-model="searchQuery"
        placeholder="搜索工具..."
        style="width: 300px; margin-right: 20px;"
        @input="handleSearch"
      >
        <template #prefix>
          <el-icon><Search /></el-icon>
        </template>
      </el-input>
      <el-select
        v-model="selectedType"
        placeholder="选择类型"
        style="width: 200px;"
        @change="handleTypeChange"
      >
        <el-option label="全部类型" value="" />
        <el-option
          v-for="type in types"
          :key="type.typeId"
          :label="type.displayName"
          :value="type.typeId"
        />
      </el-select>
    </div>

    <!-- 调试信息 -->
    <div v-if="tools.length === 0 && !loading" class="debug-info">
      <el-alert
        title="调试信息"
        type="info"
        :closable="false"
        show-icon
      >
        <p>工具数据: {{ tools.length }} 个</p>
        <p>过滤后数据: {{ filteredTools.length }} 个</p>
        <p>搜索关键词: "{{ searchQuery }}"</p>
        <p>选择类型: {{ selectedType }}</p>
        <el-button size="small" @click="debugLog">查看控制台日志</el-button>
      </el-alert>
    </div>

    <!-- 工具列表 -->
    <div class="tools-grid">
      <div v-if="loading" class="loading">
        <el-icon class="is-loading"><Loading /></el-icon>
        <span>加载中...</span>
      </div>

      <div v-else-if="filteredTools.length === 0" class="empty">
        <el-empty description="暂无工具数据">
          <el-button type="primary" @click="fetchTools">重新加载</el-button>
        </el-empty>
      </div>

      <div v-else class="tools-list">
        <div
          v-for="tool in filteredTools"
          :key="tool.toolId"
          class="tool-item"
        >
          <div class="tool-header">
            <h3>{{ tool.toolName }}</h3>
            <el-tag :type="getHealthTagType(tool.isHealth)">
              {{ tool.isHealth ? '健康' : '异常' }}
            </el-tag>
          </div>

          <div class="tool-content">
            <p class="tool-description">{{ tool.description }}</p>

            <div class="tool-meta">
              <div class="meta-item">
                <span class="label">供应商:</span>
                <span class="value">{{ tool.vendor }}</span>
              </div>
              <div class="meta-item">
                <span class="label">版本:</span>
                <span class="value">{{ tool.version }}</span>
              </div>
              <div class="meta-item">
                <span class="label">类型:</span>
                <span class="value">{{ getTypeName(tool.typeId) }}</span>
              </div>
            </div>

            <div class="tool-actions">
              <el-button type="primary" size="small" @click="useTool(tool)">
                使用工具
              </el-button>
              <el-button size="small" @click="viewDetails(tool)">
                查看详情
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </div>

  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { Search, Loading } from '@element-plus/icons-vue'
import { getToolsList } from '@/api/tools'
import { getTypesList } from '@/api/types'
import { ElMessage } from 'element-plus'
import { useRouter } from 'vue-router'

// 数据
const tools = ref([])
const types = ref([])
const selectedType = ref('')
const searchQuery = ref('')
const currentPage = ref(1)
const pageSize = ref(12)
const total = ref(0)
const loading = ref(false)
const router = useRouter()

// 获取工具列表
const fetchTools = async () => {
  loading.value = true
  try {
    console.log('开始获取工具列表...')

    const response = await getToolsList({
      pageNum: currentPage.value,
      pageSize: pageSize.value,
      typeId: selectedType.value || undefined,
      toolName: searchQuery.value || undefined
    })

    console.log('工具列表API响应:', response)

    if (response.code === 200) {
      const rawTools = response.rows || []
      console.log('原始工具数据:', rawTools)

      tools.value = rawTools.map(tool => {
        // 根据工具名称动态映射toolType
        const toolName = tool.toolName || ''

        if (toolName.includes('单向阀建模')) {
          tool.toolType = 'jm'
        } else if (toolName.includes('O型圈选型') || toolName.includes('O形圈选型')) {
          tool.toolType = 'o'
        } else if (toolName.includes('单向阀仿真')) {
          tool.toolType = 'fz'
        } else if (toolName.includes('单向阀建模仿真')) {
          tool.toolType = 'fzjm'
        } else {
          // 兜底：使用工具ID作为类型
          tool.toolType = `tool_${tool.toolId}`
        }

        return tool
      })
      total.value = response.total
      console.log('处理后的工具数据:', tools.value)

      if (tools.value.length > 0) {
        ElMessage.success(`成功加载 ${tools.value.length} 个工具`)
      } else {
        ElMessage.warning('暂无工具数据')
      }
    } else {
      console.error('获取工具列表失败:', response.msg)
      ElMessage.error(response.msg || '获取工具列表失败')
    }
  } catch (error) {
    console.error('获取工具列表异常:', error)
    ElMessage.error('获取工具列表失败: ' + error.message)
  } finally {
    loading.value = false
  }
}

// 获取类型列表
const fetchTypes = async () => {
  try {
    console.log('开始获取类型列表...')

    const response = await getTypesList({
      pageNum: 1,
      pageSize: 100,
      isActive: 1
    })

    console.log('类型列表API响应:', response)

    if (response.code === 200) {
      types.value = response.rows || []
      console.log('成功获取类型列表:', types.value.length, '个类型')
    } else {
      console.error('获取类型列表失败:', response.msg)
      ElMessage.error(response.msg || '获取类型列表失败')
    }
  } catch (error) {
    console.error('获取类型列表异常:', error)
    ElMessage.error('获取类型列表失败: ' + error.message)
  }
}

// 根据类型ID获取类型名称
const getTypeName = (typeId) => {
  const type = types.value.find(t => t.typeId === typeId)
  return type ? type.displayName : '未知类型'
}

// 根据类型ID获取标签类型
const getTypeTagType = (typeId) => {
  const type = types.value.find(t => t.typeId === typeId)
  if (!type) return ''

  // 根据颜色代码返回对应的标签类型
  const colorMap = {
    'success': 'success',
    'warning': 'warning',
    'danger': 'danger',
    'info': 'info'
  }
  return colorMap[type.colorCode] || 'info'
}

// 获取健康状态标签类型
const getHealthTagType = (isHealth) => {
  return isHealth ? 'success' : 'danger'
}

// 使用工具
const useTool = (tool) => {
  console.log('使用工具:', tool.toolName)
  ElMessage.info(`准备使用工具: ${tool.toolName}`)
}

// 查看详情
const viewDetails = (tool) => {
  console.log('查看工具详情:', tool.toolName)
  ElMessage.info(`查看工具详情: ${tool.toolName}`)
}

// 筛选后的工具列表
const filteredTools = computed(() => {
  let filtered = tools.value

  // 按类型过滤
  if (selectedType.value) {
    filtered = filtered.filter(tool => tool.typeId === selectedType.value)
  }

  // 按搜索关键词过滤
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(tool =>
      tool.toolName.toLowerCase().includes(query) ||
      tool.description.toLowerCase().includes(query) ||
      tool.vendor.toLowerCase().includes(query)
    )
  }

  return filtered
})

// 事件处理
const handleTypeChange = () => {
  console.log('类型筛选变更:', selectedType.value)
  currentPage.value = 1
  // 不需要重新fetchTools，因为使用了计算属性filteredTools进行前端过滤
}

const handleSearch = () => {
  console.log('搜索关键词变更:', searchQuery.value)
  currentPage.value = 1
  // 不需要重新fetchTools，因为使用了计算属性filteredTools进行前端过滤
}

const handleSizeChange = (val) => {
  pageSize.value = val
  fetchTools()
}

const handleCurrentChange = (val) => {
  currentPage.value = val
  fetchTools()
}

// 双击功能已注释
// function goToDetails(tool) {
//   if (tool && tool.toolType) {
//     router.push(`/tools/details/${tool.toolType}`)
//   } else {
//     console.error('工具类型(toolType)不存在', tool)
//     ElMessage.error('无法跳转：工具类型未定义')
//   }
// }

// 生命周期钩子
onMounted(() => {
  fetchTypes()
  fetchTools()
})
</script>

<style scoped>
.tools-container {
  padding: 20px;
  background: #f5f7fa;
  min-height: 100vh;
}

.tools-header {
  margin-bottom: 20px;
}

.tools-header h1 {
  margin: 0 0 10px 0;
  color: #303133;
}

.tools-header p {
  margin: 0;
  color: #606266;
}

.tools-filter {
  margin-bottom: 20px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.tools-grid {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 20px;
}

.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
  color: #606266;
}

.loading .el-icon {
  margin-right: 10px;
  font-size: 20px;
}

.empty {
  padding: 40px;
}

.debug-info {
  margin-bottom: 20px;
}

.tools-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
}

.tool-item {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 20px;
  background: #fafafa;
  transition: all 0.3s;
}

.tool-item:hover {
  border-color: #409eff;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.tool-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.tool-header h3 {
  margin: 0;
  color: #303133;
  font-size: 16px;
}

.tool-content {
  color: #606266;
}

.tool-description {
  margin: 0 0 15px 0;
  line-height: 1.5;
}

.tool-meta {
  margin-bottom: 15px;
}

.meta-item {
  display: flex;
  margin-bottom: 5px;
}

.meta-item .label {
  width: 60px;
  color: #909399;
  font-size: 12px;
}

.meta-item .value {
  color: #606266;
  font-size: 12px;
}

.tool-actions {
  display: flex;
  gap: 10px;
}
</style>

:deep(.el-radio-button.is-active) .el-radio-button__inner {
  background-color: #409eff !important;
  border-color: #409eff !important;
  color: #ffffff !important;
}

:deep(.el-tag) {
  border-radius: 6px;
  font-weight: 500;
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: none;
  background: rgba(255, 255, 255, 0.8);
}

.tools-list {
  margin-bottom: 30px;
}

.tool-card {
  margin-bottom: 24px;
  height: 20vh;
  border: none;
  border-radius: 16px;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  overflow: hidden;
  
  &:hover {
    transform: translateY(-8px);
    box-shadow: 0 16px 48px rgba(102, 126, 234, 0.2);
  }
  
  :deep(.el-card__header) {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    padding: 20px;
  }
  
  :deep(.el-card__body) {
    padding: 20px;
  }
}

.tool-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 12px;
}

.tool-name {
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  
  :deep(.el-tag) {
    border-radius: 6px;
    font-weight: 500;
    border: none;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
}

.tool-content {
  height: 7.5vw;
  max-height: 10%;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.tool-description {
  flex: 1;
  margin: 0;
  color: #606266;
  line-height: 1.6;
  font-size: 14px;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.tool-footer {
  display: flex;
  justify-content: space-between;
  color: #909399;
  font-size: 12px;
  font-weight: 500;
  padding-top: 12px;
  border-top: 1px solid rgba(102, 126, 234, 0.1);
  
  .tool-vendor, .tool-version {
    background: rgba(255, 255, 255, 0.5);
    padding: 4px 8px;
    border-radius: 4px;
  }
}

.pagination-container {
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  justify-content: center;
  padding: 15px 25px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  z-index: 1000;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .app-container {
    padding: 20px;
  }
  
  .filter-container,
  .search-container {
    padding: 20px;
  }
  
  .search-input {
    width: 100%;
  }
  
  .tool-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .tool-footer {
    flex-direction: column;
    gap: 8px;
  }
}
</style> 