# 权限管理系统文档（适配现有菜单结构）

## 概述

本系统实现了基于角色的访问控制（RBAC），支持四级权限体系：超级管理员、项目管理员、项目成员、普通用户。系统遵循最小权限原则，确保用户只能访问其职责范围内的资源。

**重要说明**：本权限系统适配现有的菜单结构，不新增菜单栏，保持原有的"项目看板"和"数据库管理"两个主菜单。

## 角色体系

### 1. 超级管理员 (admin)
- **权限范围**: 系统全权限
- **特殊权限**: 用户角色分配权限
- **功能特点**: 无任何访问限制
- **权限标识**: `*:*:*`

### 2. 项目管理员 (project_manager)
- **核心权限**:
  - 负责项目的全生命周期管理（CRUD）
  - 项目成员管理
  - 项目任务管理
  - 项目数据管理
- **访问限制**: 仅可访问自己负责的项目
- **菜单权限**: 首页 + 工具广场 + 项目看板 + 数据库管理

### 3. 项目成员 (project_member)
- **基础权限**:
  - 查看项目组内所有任务
  - 创建并执行新任务
- **操作限制**:
  - 仅可修改/提交/删除/下载自己创建的任务
  - 对其他成员任务仅具查看权限
  - 无数据库管理菜单访问权
- **菜单权限**: 首页 + 工具广场 + 项目看板（查看）

### 4. 普通用户 (common)
- **默认权限**: 仅可访问首页和工具广场
- **权限升级路径**:
  - 被赋予项目成员角色后获得项目看板菜单
  - 被赋予项目管理员角色后获得项目看板+数据库管理菜单

## 现有菜单结构

系统保持现有的菜单结构，不新增菜单栏：

1. **首页** (`/index`) - 所有用户可访问
2. **工具广场** (`/tools`) - 所有用户可访问
3. **项目看板** (`/projects`) - 项目成员及以上角色可访问
   - 项目看板 (`/projects/board`)
   - 项目详情 (`/projects/detail/:id`)
   - 创建任务 (`/projects/create-task/:projectId`)
4. **数据库管理** (`/knowledge-bases`) - 项目管理员及以上角色可访问
   - 数据库管理 (`/knowledge-bases/index`)
   - 数据库详情 (`/knowledge-bases/detail/:id`)

## 权限配置

### 权限常量定义

```python
# 角色常量
class RoleKeys:
    ADMIN = 'admin'
    PROJECT_MANAGER = 'project_manager'
    PROJECT_MEMBER = 'project_member'
    COMMON = 'common'

# 项目内角色常量
class ProjectRoles:
    MANAGER = 'manager'  # 项目管理员
    MEMBER = 'member'    # 项目成员

# 权限标识常量
class Permissions:
    ALL = '*:*:*'
    
    # 项目管理权限
    PROJECT_LIST = 'business:projects:list'
    PROJECT_ADD = 'business:projects:add'
    PROJECT_EDIT = 'business:projects:edit'
    PROJECT_REMOVE = 'business:projects:remove'
    
    # 任务管理权限
    TASK_LIST = 'business:tasks:list'
    TASK_ADD = 'business:tasks:add'
    TASK_EDIT = 'business:tasks:edit'
    TASK_REMOVE = 'business:tasks:remove'
    
    # 知识库管理权限
    KB_LIST = 'business:knowledge-bases:list'
    KB_ADD = 'business:knowledge-bases:add'
    KB_EDIT = 'business:knowledge-bases:edit'
    KB_REMOVE = 'business:knowledge-bases:remove'
```

### 数据库表结构

#### 项目成员关联表 (rd_project_members)
```sql
CREATE TABLE rd_project_members (
    project_id BIGINT NOT NULL COMMENT '项目ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    role VARCHAR(20) NOT NULL DEFAULT 'member' COMMENT '项目角色：manager(项目管理员), member(项目成员)',
    assigned_at TIMESTAMP NULL COMMENT '分配时间',
    assigned_by BIGINT NOT NULL COMMENT '分配者ID',
    PRIMARY KEY (project_id, user_id)
);
```

## 权限验证机制

### 后端权限验证

#### 1. 项目权限验证中间件
```python
from module_admin.aspect.project_auth import CheckProjectPermission

@CheckProjectPermission('member', 'project_id')
async def some_project_function(project_id: int):
    # 函数实现
    pass
```

#### 2. 任务权限验证
```python
from module_admin.aspect.project_auth import CheckTaskPermission

@CheckTaskPermission('edit', 'task_id')
async def edit_task_function(task_id: int):
    # 函数实现
    pass
```

#### 3. 服务层权限检查
```python
# 检查任务权限
has_permission = await TasksService.check_task_permission(
    query_db, task_id, user_id, user_permissions, 'edit'
)

# 检查项目成员权限
has_permission = await ProjectMembersService.check_user_project_permission_services(
    query_db, user_id, project_id, 'manager'
)
```

### 前端权限控制

#### 1. 权限指令
```vue
<!-- 角色权限控制 -->
<el-button v-hasProjectRole="['project_manager']">项目管理</el-button>

<!-- 权限标识控制 -->
<el-button v-hasProjectPermi="['business:projects:add']">新增项目</el-button>
```

#### 2. 组合式API
```javascript
import { useProjectPermission } from '@/composables/useProjectPermission'

export default {
  setup() {
    const {
      isAdmin,
      isProjectManager,
      checkTaskPermission,
      shouldShowButton
    } = useProjectPermission()
    
    return {
      isAdmin,
      isProjectManager,
      checkTaskPermission,
      shouldShowButton
    }
  }
}
```

#### 3. 路由权限守卫
```javascript
// 在路由配置中设置权限要求
{
  path: '/projects',
  component: ProjectList,
  meta: {
    roles: ['project_manager', 'project_member'],
    permissions: ['business:projects:list']
  }
}
```

## 权限边界规则

### 项目权限边界
1. **项目查看**: 项目成员及以上角色
2. **项目编辑**: 项目所有者或项目管理员
3. **项目删除**: 项目所有者或超级管理员
4. **成员管理**: 项目所有者或项目管理员

### 任务权限边界
1. **任务查看**: 项目成员（仅限项目内任务）
2. **任务创建**: 项目成员及以上角色
3. **任务编辑**: 任务创建者或项目管理员
4. **任务删除**: 任务创建者或项目管理员

### 数据库管理权限边界
1. **知识库查看**: 项目管理员及以上角色
2. **知识库管理**: 项目管理员及以上角色

## 部署和配置

### 1. 数据库初始化
```bash
# 执行菜单配置
mysql -u username -p database_name < sql/business_menus.sql

# 执行角色配置
mysql -u username -p database_name < sql/setup_project_roles.sql

# 执行权限配置
mysql -u username -p database_name < sql/role_menu_permissions.sql

# 创建项目成员表
mysql -u username -p database_name < sql/create_project_members_table.sql
```

### 2. 后端配置
```python
# 在main.py中注册项目成员控制器
from module_business.controller.project_members_controller import projectMembersController
app.include_router(projectMembersController)
```

### 3. 前端配置
```javascript
// 在main.js中注册权限指令
import permission from '@/directive/permission'
app.use(permission)

// 在需要的组件中使用权限混入
import projectPermissionMixin from '@/mixins/projectPermission'
export default {
  mixins: [projectPermissionMixin]
}
```

## 测试验证

### 运行权限测试
```bash
# 运行单元测试
python -m pytest tests/test_project_permissions.py -v

# 运行场景测试
python tests/test_permission_scenarios.py
```

### 测试覆盖的场景
1. 各角色的基本权限验证
2. 项目权限边界测试
3. 任务权限边界测试
4. 跨项目访问控制测试
5. 权限升级和降级测试

## 常见问题

### Q: 如何为用户分配项目角色？
A: 通过项目成员管理接口，由项目管理员或超级管理员分配。

### Q: 普通用户如何获得项目访问权限？
A: 需要被项目管理员添加为项目成员，或者被超级管理员直接分配项目管理员角色。

### Q: 如何实现细粒度的权限控制？
A: 可以通过扩展权限标识和在业务逻辑中添加更多的权限检查点来实现。

### Q: 权限缓存如何处理？
A: 建议在用户权限变更后清除相关缓存，或者设置较短的缓存过期时间。

## 安全注意事项

1. **最小权限原则**: 用户只应获得完成其工作所需的最小权限
2. **权限验证**: 所有敏感操作都应进行权限验证
3. **审计日志**: 记录所有权限相关的操作日志
4. **定期审查**: 定期审查用户权限，及时回收不必要的权限
5. **权限分离**: 避免单一用户拥有过多权限

## 扩展指南

### 添加新角色
1. 在 `RoleKeys` 中定义新角色常量
2. 在数据库中添加角色记录
3. 配置角色对应的菜单权限
4. 更新权限验证逻辑

### 添加新权限
1. 在 `Permissions` 中定义新权限常量
2. 在菜单表中添加对应的权限标识
3. 在控制器中添加权限验证
4. 更新前端权限控制逻辑
