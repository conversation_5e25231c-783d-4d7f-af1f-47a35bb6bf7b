-- 业务菜单权限配置SQL
-- 执行时间: 2024年
-- 描述: 为现有菜单配置权限标识，不新增菜单，只配置权限

-- 注意：此脚本不创建新菜单，只为现有菜单添加权限配置
-- 现有菜单结构：
-- 1. 首页 (/index)
-- 2. 工具广场 (/tools)
-- 3. 项目看板 (/projects) - 包含项目看板、项目详情、创建任务
-- 4. 数据库管理 (/knowledge-bases) - 包含数据库管理、数据库详情

-- 为现有菜单添加权限标识（如果菜单已存在但缺少权限标识）
-- 更新工具广场菜单权限
UPDATE sys_menu SET perms = 'business:tools:list' WHERE path = 'tools' AND perms IS NULL OR perms = '';

-- 更新项目看板菜单权限
UPDATE sys_menu SET perms = 'business:projects:list' WHERE path = 'projects' AND perms IS NULL OR perms = '';

-- 更新数据库管理菜单权限
UPDATE sys_menu SET perms = 'business:knowledge-bases:list' WHERE path = 'knowledge-bases' AND perms IS NULL OR perms = '';

-- 为现有菜单添加按钮权限（如果需要细粒度控制）
-- 注意：这些是功能按钮权限，不是菜单项

-- 工具相关按钮权限（如果需要）
-- INSERT INTO sys_menu VALUES('2100', '工具查询', (SELECT menu_id FROM sys_menu WHERE path = 'tools'), '1', '', '', '', '', 1, 0, 'F', '0', '0', 'business:tools:query', '#', 'admin', NOW(), '', null, '');
-- INSERT INTO sys_menu VALUES('2101', '工具使用', (SELECT menu_id FROM sys_menu WHERE path = 'tools'), '2', '', '', '', '', 1, 0, 'F', '0', '0', 'business:tools:use', '#', 'admin', NOW(), '', null, '');

-- 注意：
-- 1. 此脚本只为现有菜单配置权限，不创建新菜单
-- 2. 现有菜单结构保持不变
-- 3. 权限控制通过后端接口和前端路由守卫实现
-- 4. 执行后需要配置角色菜单权限关联
