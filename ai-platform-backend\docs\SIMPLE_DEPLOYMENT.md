# 权限系统简化部署指南

## 概述

本权限系统**不会新增任何菜单栏**，只对现有的"项目看板"和"数据库管理"菜单进行权限控制。

## 现有菜单结构

- **首页** - 所有用户可访问
- **工具广场** - 所有用户可访问  
- **项目看板** - 项目成员及以上角色可访问
- **数据库管理** - 项目管理员及以上角色可访问

## 快速部署

### 1. 执行数据库脚本

```bash
# 1. 备份数据库（重要！）
mysqldump -u username -p database_name > backup_$(date +%Y%m%d_%H%M%S).sql

# 2. 执行权限修复脚本
mysql -u username -p database_name < sql/fix_permissions.sql

# 3. 验证配置是否正确
mysql -u username -p database_name < sql/verify_permissions.sql
```

### 2. 后端配置

在 `server.py` 中添加项目成员控制器：

```python
from module_business.controller.project_members_controller import projectMembersController

# 在controller_list中添加
{'router': projectMembersController, 'tags': ['项目成员管理']},
```

### 3. 前端配置

在 `main.js` 中注册权限指令：

```javascript
import permission from '@/directive/permission'
app.use(permission)
```

## 角色权限说明

### 超级管理员 (admin)
- 访问所有功能，无限制

### 项目管理员 (project_manager) 
- 首页 + 工具广场 + 项目看板 + 数据库管理
- 可以管理项目成员
- 可以编辑所有项目内任务

### 项目成员 (project_member)
- 首页 + 工具广场 + 项目看板
- 只能编辑自己创建的任务
- 不能访问数据库管理

### 普通用户 (common)
- 首页 + 工具广场
- 需要被添加为项目成员才能访问项目看板

## 用户角色分配

```sql
-- 分配项目管理员角色
INSERT INTO sys_user_role (user_id, role_id) VALUES (用户ID, 100);

-- 分配项目成员角色  
INSERT INTO sys_user_role (user_id, role_id) VALUES (用户ID, 101);

-- 添加用户到具体项目
INSERT INTO rd_project_members (project_id, user_id, role, assigned_by) 
VALUES (项目ID, 用户ID, 'manager', 管理员ID);
```

## 验证部署

### 1. 数据库验证
```sql
-- 检查角色是否创建成功
SELECT * FROM sys_role WHERE role_key IN ('project_manager', 'project_member');

-- 检查菜单权限是否配置正确
SELECT r.role_name, m.menu_name, m.path 
FROM sys_role r 
JOIN sys_role_menu rm ON r.role_id = rm.role_id 
JOIN sys_menu m ON rm.menu_id = m.menu_id 
WHERE r.role_key IN ('project_manager', 'project_member');
```

### 2. 功能验证
1. 用不同角色的用户登录
2. 检查菜单显示是否符合权限设置
3. 尝试访问不同页面，确认权限控制生效
4. 确认没有404页面或重复菜单

## 常见问题

### Q: 部署后看不到权限效果？
A: 
1. 检查用户是否分配了正确的角色
2. 清除浏览器缓存重新登录
3. 检查数据库脚本是否执行成功

### Q: 用户无法访问项目看板？
A: 
1. 确认用户有项目成员或项目管理员角色
2. 确认用户被添加到具体项目中
3. 检查项目成员表数据

### Q: 如何回滚？
A: 执行回滚脚本
```bash
mysql -u username -p database_name < sql/rollback_permissions.sql
```

## 注意事项

1. **不会新增菜单** - 只对现有菜单进行权限控制
2. **数据备份** - 执行前务必备份数据库
3. **测试验证** - 建议先在测试环境验证
4. **用户通知** - 部署后通知用户权限变更

## 技术支持

如遇问题，请检查：
1. 数据库脚本执行日志
2. 后端服务启动日志  
3. 前端浏览器控制台错误
4. 用户角色和权限配置
