import { checkPermi } from '@/utils/permission'
import { shouldShowButton } from '@/utils/projectPermission'

export default {
  mounted(el, binding, vnode) {
    const { value } = binding

    if (value && value instanceof Array && value.length > 0) {
      const requiredPermissions = value
      const hasPermission = shouldShowButton(requiredPermissions)

      if (!hasPermission) {
        el.parentNode && el.parentNode.removeChild(el)
      }
    } else {
      throw new Error(`need permissions! Like v-hasProjectPermi="['business:projects:add','business:projects:edit']"`)
    }
  }
}
