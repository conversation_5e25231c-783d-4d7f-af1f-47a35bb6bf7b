from fastapi import Depends
from typing import Union, List
from exceptions.exception import PermissionException
from module_admin.entity.vo.user_vo import CurrentUserModel
from module_admin.service.login_service import LoginService
from module_business.service.project_members_service import ProjectMembersService
from module_business.service.projects_service import ProjectsService
from module_business.service.tasks_service import TasksService
from config.get_db import get_db
from config.permissions import Permissions, RoleKeys, ProjectRoles
from sqlalchemy.ext.asyncio import AsyncSession


class CheckProjectPermission:
    """
    校验当前用户是否具有项目相关权限
    """

    def __init__(self, permission_type: str = 'member', project_id_param: str = 'project_id'):
        """
        校验当前用户是否具有项目权限

        :param permission_type: 权限类型 ('member': 项目成员, 'manager': 项目管理员, 'owner': 项目所有者)
        :param project_id_param: 项目ID参数名称
        """
        self.permission_type = permission_type
        self.project_id_param = project_id_param

    def __call__(self, 
                 current_user: CurrentUserModel = Depends(LoginService.get_current_user),
                 query_db: AsyncSession = Depends(get_db)):
        async def check_permission(**kwargs):
            # 超级管理员拥有所有权限
            if Permissions.ALL in current_user.permissions:
                return True
            
            # 获取项目ID
            project_id = kwargs.get(self.project_id_param)
            if not project_id:
                raise PermissionException(data='', message='项目ID参数缺失')
            
            user_id = current_user.user.user_id
            
            # 检查项目所有者权限
            if self.permission_type == 'owner':
                project_info = await ProjectsService.projects_detail_services(query_db, project_id)
                if project_info.owner_id == user_id:
                    return True
                raise PermissionException(data='', message='您不是该项目的所有者')
            
            # 检查项目管理员权限
            elif self.permission_type == 'manager':
                has_permission = await ProjectMembersService.check_user_project_permission_services(
                    query_db, user_id, project_id, ProjectRoles.MANAGER
                )
                if has_permission:
                    return True
                
                # 检查是否是项目所有者
                project_info = await ProjectsService.projects_detail_services(query_db, project_id)
                if project_info.owner_id == user_id:
                    return True
                
                raise PermissionException(data='', message='您不是该项目的管理员')
            
            # 检查项目成员权限
            elif self.permission_type == 'member':
                has_permission = await ProjectMembersService.check_user_project_permission_services(
                    query_db, user_id, project_id
                )
                if has_permission:
                    return True
                
                # 检查是否是项目所有者
                project_info = await ProjectsService.projects_detail_services(query_db, project_id)
                if project_info.owner_id == user_id:
                    return True
                
                raise PermissionException(data='', message='您不是该项目的成员')
            
            else:
                raise PermissionException(data='', message='未知的权限类型')
        
        return check_permission


class CheckTaskPermission:
    """
    校验当前用户是否具有任务相关权限
    """

    def __init__(self, permission_type: str = 'view', task_id_param: str = 'task_id'):
        """
        校验当前用户是否具有任务权限

        :param permission_type: 权限类型 ('view': 查看, 'edit': 编辑, 'delete': 删除, 'create': 创建)
        :param task_id_param: 任务ID参数名称
        """
        self.permission_type = permission_type
        self.task_id_param = task_id_param

    def __call__(self, 
                 current_user: CurrentUserModel = Depends(LoginService.get_current_user),
                 query_db: AsyncSession = Depends(get_db)):
        async def check_permission(**kwargs):
            # 超级管理员拥有所有权限
            if Permissions.ALL in current_user.permissions:
                return True
            
            user_id = current_user.user.user_id
            
            # 创建任务权限检查（需要是项目成员）
            if self.permission_type == 'create':
                project_id = kwargs.get('project_id')
                if not project_id:
                    raise PermissionException(data='', message='项目ID参数缺失')
                
                # 检查是否是项目成员
                has_permission = await ProjectMembersService.check_user_project_permission_services(
                    query_db, user_id, project_id
                )
                if has_permission:
                    return True
                
                # 检查是否是项目所有者
                project_info = await ProjectsService.projects_detail_services(query_db, project_id)
                if project_info.owner_id == user_id:
                    return True
                
                raise PermissionException(data='', message='您不是该项目的成员，无法创建任务')
            
            # 其他任务权限检查
            task_id = kwargs.get(self.task_id_param)
            if not task_id:
                raise PermissionException(data='', message='任务ID参数缺失')
            
            # 获取任务信息
            task_info = await TasksService.tasks_detail_services(query_db, task_id)
            if not task_info.task_id:
                raise PermissionException(data='', message='任务不存在')
            
            project_id = task_info.project_id
            
            # 查看权限：项目成员即可
            if self.permission_type == 'view':
                # 检查是否是项目成员
                has_permission = await ProjectMembersService.check_user_project_permission_services(
                    query_db, user_id, project_id
                )
                if has_permission:
                    return True
                
                # 检查是否是项目所有者
                project_info = await ProjectsService.projects_detail_services(query_db, project_id)
                if project_info.owner_id == user_id:
                    return True
                
                raise PermissionException(data='', message='您没有查看该任务的权限')
            
            # 编辑/删除权限：任务创建者或项目管理员
            elif self.permission_type in ['edit', 'delete']:
                # 检查是否是任务创建者
                if task_info.assigned_to == user_id:
                    return True
                
                # 检查是否是项目管理员
                has_manager_permission = await ProjectMembersService.check_user_project_permission_services(
                    query_db, user_id, project_id, ProjectRoles.MANAGER
                )
                if has_manager_permission:
                    return True
                
                # 检查是否是项目所有者
                project_info = await ProjectsService.projects_detail_services(query_db, project_id)
                if project_info.owner_id == user_id:
                    return True
                
                action_name = '编辑' if self.permission_type == 'edit' else '删除'
                raise PermissionException(data='', message=f'您没有{action_name}该任务的权限')
            
            else:
                raise PermissionException(data='', message='未知的权限类型')
        
        return check_permission


class CheckProjectOwnership:
    """
    校验当前用户是否是项目所有者
    """

    def __init__(self, project_id_param: str = 'project_id'):
        """
        校验当前用户是否是项目所有者

        :param project_id_param: 项目ID参数名称
        """
        self.project_id_param = project_id_param

    def __call__(self, 
                 current_user: CurrentUserModel = Depends(LoginService.get_current_user),
                 query_db: AsyncSession = Depends(get_db)):
        async def check_ownership(**kwargs):
            # 超级管理员拥有所有权限
            if Permissions.ALL in current_user.permissions:
                return True
            
            # 获取项目ID
            project_id = kwargs.get(self.project_id_param)
            if not project_id:
                raise PermissionException(data='', message='项目ID参数缺失')
            
            user_id = current_user.user.user_id
            
            # 检查项目所有者
            project_info = await ProjectsService.projects_detail_services(query_db, project_id)
            if project_info.owner_id == user_id:
                return True
            
            raise PermissionException(data='', message='您不是该项目的所有者')
        
        return check_ownership


def check_project_member_permission(project_id: int, user_id: int, query_db: AsyncSession):
    """
    检查用户是否是项目成员的工具函数
    
    :param project_id: 项目ID
    :param user_id: 用户ID
    :param query_db: 数据库会话
    :return: 是否有权限
    """
    async def _check():
        # 检查是否是项目成员
        has_permission = await ProjectMembersService.check_user_project_permission_services(
            query_db, user_id, project_id
        )
        if has_permission:
            return True
        
        # 检查是否是项目所有者
        project_info = await ProjectsService.projects_detail_services(query_db, project_id)
        if project_info.owner_id == user_id:
            return True
        
        return False
    
    return _check()


def check_project_manager_permission(project_id: int, user_id: int, query_db: AsyncSession):
    """
    检查用户是否是项目管理员的工具函数
    
    :param project_id: 项目ID
    :param user_id: 用户ID
    :param query_db: 数据库会话
    :return: 是否有权限
    """
    async def _check():
        # 检查是否是项目管理员
        has_permission = await ProjectMembersService.check_user_project_permission_services(
            query_db, user_id, project_id, ProjectRoles.MANAGER
        )
        if has_permission:
            return True
        
        # 检查是否是项目所有者
        project_info = await ProjectsService.projects_detail_services(query_db, project_id)
        if project_info.owner_id == user_id:
            return True
        
        return False
    
    return _check()


def check_task_creator_permission(task_id: int, user_id: int, query_db: AsyncSession):
    """
    检查用户是否是任务创建者的工具函数
    
    :param task_id: 任务ID
    :param user_id: 用户ID
    :param query_db: 数据库会话
    :return: 是否有权限
    """
    async def _check():
        # 获取任务信息
        task_info = await TasksService.tasks_detail_services(query_db, task_id)
        if not task_info.task_id:
            return False
        
        # 检查是否是任务创建者
        if task_info.assigned_to == user_id:
            return True
        
        return False
    
    return _check()
