from contextlib import asynccontextmanager
from fastapi import FastAP<PERSON>
from config.env import AppConfig
from config.get_db import init_create_table
from config.get_redis import RedisUtil
from config.get_scheduler import SchedulerUtil
from exceptions.handle import handle_exception
from middlewares.handle import handle_middleware
from module_admin.controller.cache_controller import cacheController
from module_admin.controller.captcha_controller import captchaController
from module_admin.controller.common_controller import commonController
from module_admin.controller.config_controller import configController
from module_admin.controller.dept_controller import deptController
from module_admin.controller.dict_controller import dictController
from module_admin.controller.log_controller import logController
from module_admin.controller.login_controller import loginController
from module_admin.controller.job_controller import jobController
from module_admin.controller.menu_controller import menuController
from module_admin.controller.notice_controller import noticeController
from module_admin.controller.online_controller import onlineController
from module_admin.controller.post_controler import postController
from module_admin.controller.role_controller import roleController
from module_admin.controller.server_controller import serverController
from module_admin.controller.external_service_controller import externalServiceController
from module_admin.controller.user_controller import userController
from module_generator.controller.gen_controller import genController
from module_business.controller.tools_controller import toolsController
from module_business.controller.project_types_rel_controller import project_types_relController
from module_business.controller.projects_controller import projectsController
from module_business.controller.tasks_controller import tasksController
from module_business.controller.types_controller import typesController
from module_business.controller.knowledge_bases_controller import knowledge_basesController
from module_business.controller.kb_types_rel_controller import kb_types_relController
from module_business.controller.files_controller import filesController
from module_business.controller.task_queue_controller import taskQueueController
from module_business.controller.task_callback_controller import taskCallbackController
from module_business.controller.task_log_controller import taskLogController, taskLogWebSocketController
from module_scheduler.service.scheduler_manager import scheduler_manager
from module_log.service.log_service import start_log_service, stop_log_service
from sub_applications.handle import handle_sub_applications
from utils.log_util import logger


# 生命周期事件
@asynccontextmanager
async def lifespan(app: FastAPI):
    logger.info(f'{AppConfig.app_name}开始启动')
    await init_create_table()
    app.state.redis = await RedisUtil.create_redis_pool()
    await RedisUtil.init_sys_dict(app.state.redis)
    await RedisUtil.init_sys_config(app.state.redis)
    await SchedulerUtil.init_system_scheduler()
    
    # 初始化并启动任务调度器
    from config.get_db import AsyncSessionLocal
    async with AsyncSessionLocal() as db_session:
        try:
            await scheduler_manager.initialize(app.state.redis, db_session)
            success = await scheduler_manager.start_scheduler()
            if success:
                logger.info("任务调度器启动成功")
            else:
                logger.error("任务调度器启动失败")
        except Exception as e:
            logger.error(f"初始化任务调度器失败: {e}")
    
    # 启动日志服务
    try:
        start_log_service()
        logger.info("日志服务启动成功")
    except Exception as e:
        logger.warning(f"日志服务启动失败: {e}")

    # 启动日志调度器
    try:
        from utils.log_scheduler import start_log_scheduler
        await start_log_scheduler()
        logger.info("日志调度器启动成功")
    except Exception as e:
        logger.warning(f"日志调度器启动失败: {e}")

    logger.info(f'{AppConfig.app_name}启动成功')
    yield

    # 停止日志调度器
    try:
        from utils.log_scheduler import stop_log_scheduler
        await stop_log_scheduler()
        logger.info("日志调度器已停止")
    except Exception as e:
        logger.warning(f"停止日志调度器失败: {e}")

    # 停止日志服务
    try:
        stop_log_service()
        logger.info("日志服务已停止")
    except Exception as e:
        logger.warning(f"停止日志服务失败: {e}")

    # 停止任务调度器
    await scheduler_manager.stop_scheduler()
    await RedisUtil.close_redis_pool(app)
    await SchedulerUtil.close_system_scheduler()


# 初始化FastAPI对象
app = FastAPI(
    title=AppConfig.app_name,
    description=f'{AppConfig.app_name}接口文档',
    version=AppConfig.app_version,
    lifespan=lifespan,
)

# 挂载子应用
handle_sub_applications(app)
# 加载中间件处理方法
handle_middleware(app)
# 加载全局异常处理方法
handle_exception(app)


# 加载路由列表
controller_list = [
    {'router': loginController, 'tags': ['登录模块']},
    {'router': captchaController, 'tags': ['验证码模块']},
    {'router': userController, 'tags': ['系统管理-用户管理']},
    {'router': roleController, 'tags': ['系统管理-角色管理']},
    {'router': menuController, 'tags': ['系统管理-菜单管理']},
    {'router': deptController, 'tags': ['系统管理-部门管理']},
    {'router': postController, 'tags': ['系统管理-岗位管理']},
    {'router': dictController, 'tags': ['系统管理-字典管理']},
    {'router': configController, 'tags': ['系统管理-参数管理']},
    {'router': noticeController, 'tags': ['系统管理-通知公告管理']},
    {'router': logController, 'tags': ['系统管理-日志管理']},
    {'router': onlineController, 'tags': ['系统监控-在线用户']},
    {'router': jobController, 'tags': ['系统监控-定时任务']},
    {'router': serverController, 'tags': ['系统监控-服务监控']},
    {'router': externalServiceController, 'tags': ['系统监控-外部服务监控']},
    {'router': cacheController, 'tags': ['系统监控-缓存监控']},
    {'router': commonController, 'tags': ['通用模块']},
    {'router': genController, 'tags': ['代码生成']},
    {'router': toolsController, 'tags': ['工具管理']},
    {'router': project_types_relController, 'tags': ['项目-类型关联关系']},
    {'router': projectsController, 'tags': ['项目信息管理']},
    {'router': tasksController, 'tags': ['任务信息管理']},
    {'router': typesController, 'tags': ['通用类型管理']},
    {'router': knowledge_basesController, 'tags': ['数据库管理']},
    {'router': kb_types_relController, 'tags': ['数据库-类型关联关系']},
    {'router': filesController, 'tags': ['数据库文件存储']},
    {'router': taskQueueController, 'tags': ['任务队列管理']},
    {'router': taskCallbackController, 'tags': ['任务回调管理']},
    {'router': taskLogController, 'tags': ['任务日志管理']},
    {'router': taskLogWebSocketController, 'tags': ['任务日志WebSocket']},
]

for controller in controller_list:
    app.include_router(router=controller.get('router'), tags=controller.get('tags'))
