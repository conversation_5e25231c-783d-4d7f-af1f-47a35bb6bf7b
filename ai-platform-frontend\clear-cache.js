const fs = require('fs')
const path = require('path')

// 清理缓存目录
const cacheDirs = [
  'node_modules/.cache',
  'dist',
  '.nuxt',
  '.vite'
]

cacheDirs.forEach(dir => {
  const fullPath = path.join(__dirname, dir)
  if (fs.existsSync(fullPath)) {
    console.log(`清理缓存目录: ${dir}`)
    fs.rmSync(fullPath, { recursive: true, force: true })
  }
})

console.log('缓存清理完成！')
console.log('请重新启动开发服务器：npm run dev')
