-- 检查当前菜单配置的SQL脚本
-- 用于诊断菜单路由问题
-- 执行时间: 2024年

-- ==========================================
-- 1. 检查所有主菜单
-- ==========================================
SELECT '=== 所有主菜单 ===' as section;
SELECT 
    menu_id,
    menu_name,
    path,
    component,
    menu_type,
    visible,
    status,
    perms,
    icon,
    order_num
FROM sys_menu 
WHERE parent_id = 0 AND menu_type = 'M'
ORDER BY order_num;

-- ==========================================
-- 2. 检查项目相关菜单
-- ==========================================
SELECT '=== 项目相关菜单 ===' as section;
SELECT 
    menu_id,
    menu_name,
    parent_id,
    path,
    component,
    menu_type,
    visible,
    status,
    perms
FROM sys_menu 
WHERE menu_name LIKE '%项目%' OR path LIKE '%project%'
ORDER BY parent_id, order_num;

-- ==========================================
-- 3. 检查数据库管理相关菜单
-- ==========================================
SELECT '=== 数据库管理相关菜单 ===' as section;
SELECT 
    menu_id,
    menu_name,
    parent_id,
    path,
    component,
    menu_type,
    visible,
    status,
    perms
FROM sys_menu 
WHERE menu_name LIKE '%数据库%' OR path LIKE '%knowledge%' OR path LIKE '%database%'
ORDER BY parent_id, order_num;

-- ==========================================
-- 4. 检查工具广场菜单
-- ==========================================
SELECT '=== 工具广场菜单 ===' as section;
SELECT 
    menu_id,
    menu_name,
    parent_id,
    path,
    component,
    menu_type,
    visible,
    status,
    perms
FROM sys_menu 
WHERE menu_name LIKE '%工具%' OR path LIKE '%tool%'
ORDER BY parent_id, order_num;

-- ==========================================
-- 5. 检查子菜单
-- ==========================================
SELECT '=== 所有子菜单 ===' as section;
SELECT 
    child.menu_id,
    child.menu_name,
    parent.menu_name as parent_name,
    child.path,
    child.component,
    child.menu_type,
    child.visible,
    child.status
FROM sys_menu child
JOIN sys_menu parent ON child.parent_id = parent.menu_id
WHERE child.parent_id != 0
ORDER BY child.parent_id, child.order_num;

-- ==========================================
-- 6. 检查重复菜单
-- ==========================================
SELECT '=== 重复菜单检查 ===' as section;
SELECT 
    menu_name,
    COUNT(*) as count,
    GROUP_CONCAT(menu_id) as menu_ids,
    GROUP_CONCAT(path) as paths
FROM sys_menu 
WHERE status = '0'
GROUP BY menu_name 
HAVING COUNT(*) > 1;

-- ==========================================
-- 7. 检查路径冲突
-- ==========================================
SELECT '=== 路径冲突检查 ===' as section;
SELECT 
    path,
    COUNT(*) as count,
    GROUP_CONCAT(menu_name) as menu_names,
    GROUP_CONCAT(menu_id) as menu_ids
FROM sys_menu 
WHERE path IS NOT NULL AND path != '' AND status = '0'
GROUP BY path 
HAVING COUNT(*) > 1;

-- ==========================================
-- 8. 检查角色菜单权限
-- ==========================================
SELECT '=== 角色菜单权限 ===' as section;
SELECT 
    r.role_name,
    r.role_key,
    m.menu_name,
    m.path,
    m.menu_type,
    m.perms
FROM sys_role r
JOIN sys_role_menu rm ON r.role_id = rm.role_id
JOIN sys_menu m ON rm.menu_id = m.menu_id
WHERE r.status = '0' AND m.status = '0'
ORDER BY r.role_id, m.parent_id, m.order_num;

-- ==========================================
-- 9. 检查孤立菜单（没有角色权限的菜单）
-- ==========================================
SELECT '=== 孤立菜单检查 ===' as section;
SELECT 
    m.menu_id,
    m.menu_name,
    m.path,
    m.menu_type
FROM sys_menu m
LEFT JOIN sys_role_menu rm ON m.menu_id = rm.menu_id
WHERE rm.menu_id IS NULL AND m.status = '0' AND m.menu_type IN ('M', 'C')
ORDER BY m.parent_id, m.order_num;

-- ==========================================
-- 10. 检查无效的角色菜单关联
-- ==========================================
SELECT '=== 无效角色菜单关联 ===' as section;
SELECT 
    rm.role_id,
    rm.menu_id,
    r.role_name,
    'Menu not found' as issue
FROM sys_role_menu rm
LEFT JOIN sys_menu m ON rm.menu_id = m.menu_id
LEFT JOIN sys_role r ON rm.role_id = r.role_id
WHERE m.menu_id IS NULL OR r.role_id IS NULL;

-- ==========================================
-- 11. 统计信息
-- ==========================================
SELECT '=== 菜单统计信息 ===' as section;
SELECT 
    '总菜单数' as item,
    COUNT(*) as count
FROM sys_menu 
WHERE status = '0'
UNION ALL
SELECT 
    '主菜单数' as item,
    COUNT(*) as count
FROM sys_menu 
WHERE parent_id = 0 AND menu_type = 'M' AND status = '0'
UNION ALL
SELECT 
    '子菜单数' as item,
    COUNT(*) as count
FROM sys_menu 
WHERE parent_id != 0 AND menu_type = 'C' AND status = '0'
UNION ALL
SELECT 
    '按钮权限数' as item,
    COUNT(*) as count
FROM sys_menu 
WHERE menu_type = 'F' AND status = '0'
UNION ALL
SELECT 
    '角色数' as item,
    COUNT(*) as count
FROM sys_role 
WHERE status = '0'
UNION ALL
SELECT 
    '角色菜单关联数' as item,
    COUNT(*) as count
FROM sys_role_menu rm
JOIN sys_role r ON rm.role_id = r.role_id
JOIN sys_menu m ON rm.menu_id = m.menu_id
WHERE r.status = '0' AND m.status = '0';

-- ==========================================
-- 12. 问题诊断
-- ==========================================
SELECT '=== 问题诊断 ===' as section;

-- 检查是否存在projects路径的菜单
SELECT 
    CASE 
        WHEN COUNT(*) > 0 THEN CONCAT('✓ 找到 ', COUNT(*), ' 个projects路径菜单')
        ELSE '✗ 未找到projects路径菜单'
    END as projects_check
FROM sys_menu 
WHERE path = 'projects' AND status = '0';

-- 检查是否存在knowledge-bases路径的菜单
SELECT 
    CASE 
        WHEN COUNT(*) > 0 THEN CONCAT('✓ 找到 ', COUNT(*), ' 个knowledge-bases路径菜单')
        ELSE '✗ 未找到knowledge-bases路径菜单'
    END as kb_check
FROM sys_menu 
WHERE path = 'knowledge-bases' AND status = '0';

-- 检查是否存在database路径的菜单（应该不存在）
SELECT 
    CASE 
        WHEN COUNT(*) > 0 THEN CONCAT('⚠ 发现 ', COUNT(*), ' 个database路径菜单（可能导致冲突）')
        ELSE '✓ 未发现database路径菜单'
    END as database_check
FROM sys_menu 
WHERE path = 'database' AND status = '0';
